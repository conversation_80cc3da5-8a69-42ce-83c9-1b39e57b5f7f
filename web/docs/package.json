{"name": "microservice-cli-docs", "version": "0.0.0", "private": true, "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "docusaurus start", "build": "docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids"}, "dependencies": {"@docusaurus/core": "^3.0.0", "@docusaurus/preset-classic": "^3.0.0", "@docusaurus/theme-live-codeblock": "^3.0.0", "@mdx-js/react": "^3.0.0", "clsx": "^2.0.0", "prism-react-renderer": "^2.1.0", "react": "^18.0.0", "react-dom": "^18.0.0"}, "devDependencies": {"@docusaurus/module-type-aliases": "^3.0.0", "@docusaurus/tsconfig": "^3.0.0", "@docusaurus/types": "^3.0.0", "typescript": "~5.2.0"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 3 chrome version", "last 3 firefox version", "last 5 safari version"]}, "engines": {"node": ">=18.0"}}