# NestJS Framework

NestJS is a progressive Node.js framework for building efficient, reliable, and scalable server-side applications. Microservice CLI generates production-ready NestJS services with microservice communication, authentication, and database integration.

## Why NestJS?

### 🏗️ Enterprise Architecture
- **Modular design** with dependency injection
- **Decorator-based** development
- **TypeScript-first** approach
- **Scalable** and maintainable codebase

### 🔄 Microservice Ready
- **Built-in microservice** support
- **Multiple transport** layers (Kafka, gRPC, Redis, etc.)
- **Message patterns** for service communication
- **Load balancing** and service discovery

### 🛡️ Production Features
- **Authentication & authorization** guards
- **Validation** pipes
- **Exception handling** filters
- **Logging** and monitoring
- **Testing** utilities

## Generated Project Structure

When you select NestJS, your service includes:

```
services/api/
├── src/
│   ├── main.ts                  # Application entry point
│   ├── app.module.ts            # Root module
│   ├── app.controller.ts        # Main controller
│   ├── app.service.ts           # Main service
│   ├── auth/                    # Authentication module
│   │   ├── auth.module.ts
│   │   ├── auth.controller.ts
│   │   ├── auth.service.ts
│   │   └── guards/
│   ├── microservice/            # Microservice communication
│   │   ├── microservice.module.ts
│   │   ├── microservice.controller.ts
│   │   └── microservice.service.ts
│   ├── database/                # Database configuration
│   │   └── database.module.ts
│   ├── config/                  # Configuration
│   │   └── configuration.ts
│   └── common/                  # Shared utilities
│       ├── filters/
│       └── interceptors/
├── test/                        # E2E tests
├── package.json
├── tsconfig.json
├── nest-cli.json
└── Dockerfile
```

## Key Features

### 1. Microservice Communication

#### Kafka Integration
```typescript
// main.ts
app.connectMicroservice<MicroserviceOptions>({
  transport: Transport.KAFKA,
  options: {
    client: {
      clientId: 'api-service',
      brokers: ['localhost:9092'],
    },
    consumer: {
      groupId: 'api-consumer',
    },
  },
});
```

#### Message Patterns
```typescript
// microservice.controller.ts
@Controller()
export class MicroserviceController {
  @MessagePattern('user.created')
  async handleUserCreated(@Payload() data: CreateUserDto) {
    return this.microserviceService.handleUserCreated(data);
  }

  @EventPattern('order.completed')
  async handleOrderCompleted(@Payload() data: OrderDto) {
    await this.microserviceService.processOrder(data);
  }
}
```

#### gRPC Support
```typescript
// main.ts
app.connectMicroservice<MicroserviceOptions>({
  transport: Transport.GRPC,
  options: {
    package: 'api',
    protoPath: join(__dirname, 'proto/service.proto'),
    url: 'localhost:5000',
  },
});
```

### 2. Authentication & Authorization

#### JWT Authentication
```typescript
// auth.service.ts
@Injectable()
export class AuthService {
  async login(loginDto: LoginDto) {
    const user = await this.validateUser(loginDto);
    const payload = { username: user.username, sub: user.id };
    
    return {
      access_token: this.jwtService.sign(payload),
    };
  }
}
```

#### Guards
```typescript
// jwt-auth.guard.ts
@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {}

// Usage in controller
@Controller('protected')
@UseGuards(JwtAuthGuard)
export class ProtectedController {
  @Get()
  getProtectedData(@Request() req) {
    return req.user;
  }
}
```

### 3. Database Integration

#### TypeORM Configuration
```typescript
// database.module.ts
@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get('database.host'),
        port: configService.get('database.port'),
        username: configService.get('database.username'),
        password: configService.get('database.password'),
        database: configService.get('database.name'),
        autoLoadEntities: true,
        synchronize: configService.get('NODE_ENV') === 'development',
      }),
      inject: [ConfigService],
    }),
  ],
})
export class DatabaseModule {}
```

#### Entity Example
```typescript
// user.entity.ts
@Entity()
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  email: string;

  @Column()
  password: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
```

### 4. Validation & DTOs

#### Data Transfer Objects
```typescript
// create-user.dto.ts
export class CreateUserDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @MinLength(8)
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, {
    message: 'Password must contain uppercase, lowercase, and number',
  })
  password: string;

  @IsString()
  @IsNotEmpty()
  name: string;
}
```

#### Validation Pipe
```typescript
// main.ts
app.useGlobalPipes(new ValidationPipe({
  whitelist: true,
  forbidNonWhitelisted: true,
  transform: true,
}));
```

### 5. Swagger Documentation

#### API Documentation
```typescript
// main.ts
const config = new DocumentBuilder()
  .setTitle('API Service')
  .setDescription('Microservice API documentation')
  .setVersion('1.0')
  .addBearerAuth()
  .build();

const document = SwaggerModule.createDocument(app, config);
SwaggerModule.setup('docs', app, document);
```

#### Controller Documentation
```typescript
// app.controller.ts
@ApiTags('App')
@Controller()
export class AppController {
  @Get()
  @ApiOperation({ summary: 'Get service information' })
  @ApiResponse({ status: 200, description: 'Service info retrieved' })
  getServiceInfo() {
    return this.appService.getServiceInfo();
  }
}
```

## Configuration

### Environment Configuration
```typescript
// configuration.ts
export default () => ({
  port: parseInt(process.env.PORT, 10) || 3000,
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT, 10) || 5432,
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
    name: process.env.DB_NAME || 'api_db',
  },
  jwt: {
    secret: process.env.JWT_SECRET || 'your-secret-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
  },
  microservices: {
    kafka: {
      brokers: [process.env.KAFKA_BROKER || 'localhost:9092'],
    },
  },
});
```

### Module Configuration
```typescript
// app.module.ts
@Module({
  imports: [
    ConfigModule.forRoot({
      load: [configuration],
      isGlobal: true,
    }),
    DatabaseModule,
    AuthModule,
    MicroserviceModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
```

## Testing

### Unit Tests
```typescript
// app.service.spec.ts
describe('AppService', () => {
  let service: AppService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AppService],
    }).compile();

    service = module.get<AppService>(AppService);
  });

  it('should return service info', () => {
    const result = service.getServiceInfo();
    expect(result).toHaveProperty('service');
    expect(result.service).toBe('api');
  });
});
```

### E2E Tests
```typescript
// app.e2e-spec.ts
describe('AppController (e2e)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  it('/ (GET)', () => {
    return request(app.getHttpServer())
      .get('/')
      .expect(200)
      .expect((res) => {
        expect(res.body).toHaveProperty('service');
      });
  });
});
```

## Deployment

### Docker Configuration
```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000

USER node

CMD ["npm", "start"]
```

### Health Checks
```typescript
// health.controller.ts
@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private db: TypeOrmHealthIndicator,
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      () => this.db.pingCheck('database'),
    ]);
  }
}
```

## Best Practices

### 1. Module Organization
- **Feature modules** for business logic
- **Shared modules** for common functionality
- **Core modules** for application-wide services

### 2. Error Handling
```typescript
// http-exception.filter.ts
@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const status = exception.getStatus();

    response.status(status).json({
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: request.url,
      message: exception.message,
    });
  }
}
```

### 3. Logging
```typescript
// logging.interceptor.ts
@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const { method, url } = request;
    
    this.logger.log(`${method} ${url}`);
    
    return next.handle();
  }
}
```

## Advanced Features

### Custom Decorators
```typescript
// user.decorator.ts
export const User = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    return request.user;
  },
);

// Usage
@Get('profile')
@UseGuards(JwtAuthGuard)
getProfile(@User() user: UserEntity) {
  return user;
}
```

### Caching
```typescript
// app.module.ts
@Module({
  imports: [
    CacheModule.register({
      ttl: 5, // seconds
      max: 10, // maximum number of items in cache
    }),
  ],
})
export class AppModule {}

// Usage
@Controller()
export class AppController {
  @Get()
  @CacheKey('service-info')
  @CacheTTL(30)
  getServiceInfo() {
    return this.appService.getServiceInfo();
  }
}
```

## Resources

- [NestJS Documentation](https://docs.nestjs.com/)
- [NestJS Microservices](https://docs.nestjs.com/microservices/basics)
- [TypeORM Documentation](https://typeorm.io/)
- [NestJS Testing](https://docs.nestjs.com/fundamentals/testing)

## Next Steps

- [Explore microservice communication](../microservices/overview)
- [Learn about database integration](../database/overview)
- [See deployment examples](../deployment/docker)
