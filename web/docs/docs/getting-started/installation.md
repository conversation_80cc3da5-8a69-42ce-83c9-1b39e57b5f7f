# Installation

Learn how to install and set up Microservice CLI on your development machine.

## Prerequisites

Before installing Microservice CLI, make sure you have the following installed:

- **Node.js** (version 16 or higher)
- **npm** or **yarn** package manager
- **Git** (for cloning templates and version control)

### Check Your Node.js Version

```bash
node --version
# Should output v16.0.0 or higher
```

## Installation Methods

### Global Installation (Recommended)

Install Microservice CLI globally to use it from anywhere:

```bash
npm install -g microservice-cli
```

Or with Yarn:

```bash
yarn global add microservice-cli
```

### Verify Installation

Check that the installation was successful:

```bash
microservice-cli --version
# Should output the current version

microservice-cli --help
# Should display help information
```

### Using npx (No Installation Required)

You can also use Microservice CLI without installing it globally:

```bash
npx microservice-cli create my-project
```

This approach downloads and runs the latest version each time.

## Platform-Specific Instructions

### Windows

1. **Install Node.js** from [nodejs.org](https://nodejs.org/)
2. **Open Command Prompt or PowerShell** as Administrator
3. **Run the installation command**:
   ```cmd
   npm install -g microservice-cli
   ```

### macOS

1. **Install Node.js** using Homebrew (recommended):
   ```bash
   brew install node
   ```
   Or download from [nodejs.org](https://nodejs.org/)

2. **Install the CLI**:
   ```bash
   npm install -g microservice-cli
   ```

### Linux (Ubuntu/Debian)

1. **Install Node.js**:
   ```bash
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs
   ```

2. **Install the CLI**:
   ```bash
   npm install -g microservice-cli
   ```

## Docker Installation

If you prefer using Docker, you can run Microservice CLI in a container:

```bash
# Pull the official image
docker pull microservicecli/cli:latest

# Create an alias for easy usage
alias microservice-cli='docker run --rm -v $(pwd):/workspace microservicecli/cli:latest'

# Use it like the regular CLI
microservice-cli create my-project
```

## Development Installation

If you want to contribute to Microservice CLI or use the latest development version:

```bash
# Clone the repository
git clone https://github.com/your-org/microservice-cli.git
cd microservice-cli

# Install dependencies
npm install

# Build the project
npm run build

# Link for global usage
npm link
```

## Updating

### Update Global Installation

```bash
npm update -g microservice-cli
```

### Check for Updates

```bash
microservice-cli --version
npm view microservice-cli version
```

## Troubleshooting

### Permission Errors (macOS/Linux)

If you encounter permission errors during global installation:

```bash
# Option 1: Use a Node version manager (recommended)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install node
npm install -g microservice-cli

# Option 2: Change npm's default directory
mkdir ~/.npm-global
npm config set prefix '~/.npm-global'
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
source ~/.bashrc
npm install -g microservice-cli
```

### Command Not Found

If `microservice-cli` command is not found after installation:

1. **Check your PATH**:
   ```bash
   echo $PATH
   npm config get prefix
   ```

2. **Add npm global bin to PATH**:
   ```bash
   echo 'export PATH=$(npm config get prefix)/bin:$PATH' >> ~/.bashrc
   source ~/.bashrc
   ```

### Windows PATH Issues

On Windows, if the command is not recognized:

1. Find your npm global directory:
   ```cmd
   npm config get prefix
   ```

2. Add it to your system PATH through:
   - Control Panel → System → Advanced → Environment Variables
   - Add the npm global directory to your PATH

## IDE Integration

### VS Code Extension

Install the official VS Code extension for enhanced development experience:

1. Open VS Code
2. Go to Extensions (Ctrl+Shift+X)
3. Search for "Microservice CLI"
4. Install the extension

### IntelliJ/WebStorm Plugin

A plugin is available for JetBrains IDEs:

1. Go to File → Settings → Plugins
2. Search for "Microservice CLI"
3. Install and restart your IDE

## Next Steps

Now that you have Microservice CLI installed, you're ready to:

- [Create your first project](./quick-start)
- [Explore the CLI commands](../cli-reference/commands)
- [Try the web generator](https://microservice-cli.dev/generator)

## Getting Help

If you encounter any issues during installation:

- Check our [FAQ](../troubleshooting/faq)
- Join our [Discord community](https://discord.gg/microservice-cli)
- [Open an issue on GitHub](https://github.com/your-org/microservice-cli/issues)
