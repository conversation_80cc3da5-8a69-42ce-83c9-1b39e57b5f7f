# Tailwind CSS v4 Integration

Microservice CLI generates projects with the latest **Tailwind CSS v4**, featuring a modern CSS-first approach, OKLCH color space, and zero-config setup.

## What's New in Tailwind v4

### 🎨 CSS-First Architecture
- **No config files** - Everything is configured in CSS
- **`@theme` blocks** - Define your design system in CSS
- **Native CSS custom properties** - Better browser support

### 🌈 OKLCH Color Space
- **Perceptually uniform** colors
- **Better color accuracy** across devices
- **Improved accessibility** with consistent contrast

### ⚡ Vite Integration
- **Faster builds** with the new Vite plugin
- **Hot module replacement** for CSS changes
- **Optimized production builds**

## Generated Project Structure

When you select Tailwind CSS, your project includes:

```
your-project/
├── frontend/
│   ├── app/globals.css          # Tailwind v4 with @theme
│   ├── vite.config.ts           # @tailwindcss/vite plugin
│   └── package.json             # tailwindcss v4 dependencies
```

## CSS Configuration

### Global Styles (globals.css)

```css
@import "tailwindcss";

@theme {
  --color-primary: oklch(47.78% 0.204 238.75);
  --color-primary-foreground: oklch(98% 0.013 285.75);
  --color-secondary: oklch(96.1% 0.013 285.75);
  --color-background: oklch(100% 0 0);
  --color-foreground: oklch(9% 0.026 285.75);
  
  @media (prefers-color-scheme: dark) {
    --color-primary: oklch(69.71% 0.131 230.32);
    --color-background: oklch(9% 0.026 285.75);
    --color-foreground: oklch(98% 0.013 285.75);
  }
}
```

### Vite Configuration

```typescript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'

export default defineConfig({
  plugins: [
    react(),
    tailwindcss(), // No config needed!
  ],
})
```

## Key Features

### 1. Zero Configuration

No `tailwind.config.js` file needed. Everything is configured in CSS:

```css
@theme {
  /* Your design system here */
  --color-brand: oklch(60% 0.15 250);
  --font-family-sans: "Inter", system-ui, sans-serif;
  --spacing-xl: 2rem;
}
```

### 2. OKLCH Colors

Better color accuracy and consistency:

```css
@theme {
  /* Traditional HSL */
  --color-old: hsl(240, 100%, 50%);
  
  /* Modern OKLCH - more perceptually uniform */
  --color-new: oklch(60% 0.15 250);
}
```

### 3. Automatic Dark Mode

Built-in dark mode support:

```css
@theme {
  --color-background: white;
  
  @media (prefers-color-scheme: dark) {
    --color-background: black;
  }
}
```

### 4. Custom Properties Integration

Seamless integration with CSS custom properties:

```css
.my-component {
  background-color: var(--color-primary);
  color: var(--color-primary-foreground);
}
```

## Usage Examples

### Basic Styling

```tsx
// React component with Tailwind v4
export function Button({ children, variant = 'primary' }) {
  return (
    <button 
      className={`
        px-4 py-2 rounded-lg font-medium transition-colors
        ${variant === 'primary' 
          ? 'bg-primary text-primary-foreground hover:bg-primary/90' 
          : 'bg-secondary text-secondary-foreground hover:bg-secondary/80'
        }
      `}
    >
      {children}
    </button>
  )
}
```

### Custom Theme Colors

```css
@theme {
  /* Brand colors */
  --color-brand-50: oklch(97% 0.01 250);
  --color-brand-500: oklch(60% 0.15 250);
  --color-brand-900: oklch(20% 0.1 250);
  
  /* Semantic colors */
  --color-success: oklch(70% 0.12 145);
  --color-warning: oklch(80% 0.15 85);
  --color-error: oklch(65% 0.2 25);
}
```

### Responsive Design

```tsx
<div className="
  grid grid-cols-1 gap-4
  md:grid-cols-2 md:gap-6
  lg:grid-cols-3 lg:gap-8
">
  {/* Content */}
</div>
```

## Migration from v3

If you're familiar with Tailwind v3, here are the key differences:

### Configuration

**Tailwind v3:**
```javascript
// tailwind.config.js
module.exports = {
  theme: {
    colors: {
      primary: '#3b82f6',
    }
  }
}
```

**Tailwind v4:**
```css
/* globals.css */
@theme {
  --color-primary: oklch(60% 0.15 250);
}
```

### Build Setup

**Tailwind v3:**
```javascript
// postcss.config.js
module.exports = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}
```

**Tailwind v4:**
```typescript
// vite.config.ts
import tailwindcss from '@tailwindcss/vite'

export default defineConfig({
  plugins: [tailwindcss()],
})
```

## Advanced Customization

### Custom Utilities

```css
@theme {
  /* Custom spacing scale */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 2rem;
  --spacing-xl: 4rem;
}

/* Custom utility classes */
.glass {
  backdrop-filter: blur(8px);
  background-color: rgb(255 255 255 / 0.8);
  border: 1px solid rgb(255 255 255 / 0.2);
}
```

### Component Variants

```css
@theme {
  /* Button variants */
  --color-button-primary: var(--color-primary);
  --color-button-secondary: var(--color-secondary);
  --color-button-danger: var(--color-error);
}
```

## Performance Benefits

### Faster Builds
- **Vite plugin** provides faster compilation
- **CSS-only** configuration reduces JavaScript overhead
- **Tree shaking** removes unused styles more efficiently

### Smaller Bundle Size
- **Optimized output** with better compression
- **Reduced runtime** JavaScript
- **Efficient** CSS generation

## Best Practices

### 1. Use Semantic Color Names

```css
@theme {
  /* Good - semantic names */
  --color-primary: oklch(60% 0.15 250);
  --color-success: oklch(70% 0.12 145);
  
  /* Avoid - specific color names */
  --color-blue-500: oklch(60% 0.15 250);
}
```

### 2. Leverage OKLCH Benefits

```css
@theme {
  /* Create consistent color scales */
  --color-gray-50: oklch(98% 0 0);
  --color-gray-100: oklch(95% 0 0);
  --color-gray-200: oklch(90% 0 0);
  --color-gray-300: oklch(85% 0 0);
  /* ... */
}
```

### 3. Design System Integration

```css
@theme {
  /* Typography scale */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  
  /* Spacing scale */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-4: 1rem;
  --spacing-8: 2rem;
}
```

## Troubleshooting

### Common Issues

1. **Styles not applying**: Make sure you're importing `@import "tailwindcss"` in your CSS
2. **Dark mode not working**: Check your `@media (prefers-color-scheme: dark)` syntax
3. **Custom colors not available**: Verify your `@theme` block syntax

### Debug Mode

Enable verbose output to debug Tailwind compilation:

```bash
# Development with debug info
npm run dev -- --debug
```

## Resources

- [Tailwind CSS v4 Documentation](https://tailwindcss.com/docs/v4-beta)
- [OKLCH Color Picker](https://oklch.com/)
- [Vite Plugin Documentation](https://tailwindcss.com/docs/installation/using-vite)

## Next Steps

- [Explore styling options](./styled-components)
- [Learn about component patterns](../templates/react)
- [See deployment examples](../deployment/vercel)
