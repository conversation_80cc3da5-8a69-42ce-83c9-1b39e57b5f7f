# Welcome to Microservice CLI

**Microservice CLI** is a powerful command-line tool that generates production-ready microservice projects with modern technologies. Build scalable microservice architectures in seconds with zero configuration.

## 🚀 Quick Start

Get started with Microservice CLI in just a few commands:

```bash
# Install globally
npm install -g microservice-cli

# Create your first project
microservice-cli create my-awesome-project

# Start developing
cd my-awesome-project
npm install
npm run dev
```

## ✨ What You Get

### 🏗️ Modern Architecture
- **NestJS** for enterprise-grade backend services
- **Next.js** for full-stack React applications
- **Tailwind CSS v4** with modern OKLCH colors
- **TypeScript** throughout for type safety

### 🔄 Microservice Communication
- **Kafka** for event streaming
- **gRPC** for high-performance RPC
- **Redis** for pub/sub messaging
- **Message patterns** for service communication

### 🗄️ Database Integration
- **PostgreSQL**, **MySQL**, **MongoDB**, **SQLite**
- **TypeORM** and **Mongoose** integration
- Database migrations and seeding

### 🐳 DevOps Ready
- **Docker** containerization
- **Docker Compose** for local development
- **Kubernetes** manifests
- **CI/CD** pipeline templates

## 🎯 Key Features

### Zero Configuration
Start building immediately without spending time on boilerplate setup.

### Production Ready
Generated projects include best practices, security, testing, and documentation.

### Highly Customizable
Choose your preferred frameworks, databases, and architectural patterns.

### Modern Stack
Always up-to-date with the latest versions and best practices.

## 🌟 Why Choose Microservice CLI?

| Feature | Microservice CLI | Manual Setup |
|---------|------------------|--------------|
| **Time to Start** | 30 seconds | Hours/Days |
| **Best Practices** | ✅ Built-in | ❌ Manual research |
| **Type Safety** | ✅ TypeScript everywhere | ❌ Optional |
| **Testing Setup** | ✅ Pre-configured | ❌ Manual setup |
| **Docker Ready** | ✅ Included | ❌ Manual configuration |
| **Documentation** | ✅ Auto-generated | ❌ Manual writing |

## 🎨 Interactive Web Generator

Don't want to use the CLI? Try our [interactive web generator](https://microservice-cli.dev/generator) to configure and download your project through a beautiful web interface.

## 📚 What's Next?

- [Installation Guide](./getting-started/installation) - Install and set up the CLI
- [Quick Start Tutorial](./getting-started/quick-start) - Create your first project
- [CLI Reference](./cli-reference/commands) - Complete command documentation
- [Templates Overview](./templates/overview) - Explore available templates

## 🤝 Community & Support

- **GitHub**: [Report issues and contribute](https://github.com/your-org/microservice-cli)
- **Discord**: [Join our community](https://discord.gg/microservice-cli)
- **Twitter**: [Follow for updates](https://twitter.com/microservice_cli)
- **Stack Overflow**: [Get help with the `microservice-cli` tag](https://stackoverflow.com/questions/tagged/microservice-cli)

---

Ready to build your next microservice? Let's get started! 🚀
