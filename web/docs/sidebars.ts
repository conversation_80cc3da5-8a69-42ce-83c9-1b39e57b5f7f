import type {SidebarsConfig} from '@docusaurus/plugin-content-docs';

const sidebars: SidebarsConfig = {
  tutorialSidebar: [
    'intro',
    {
      type: 'category',
      label: 'Getting Started',
      items: [
        'getting-started/installation',
        'getting-started/quick-start',
        'getting-started/your-first-project',
      ],
    },
    {
      type: 'category',
      label: 'CLI Reference',
      items: [
        'cli-reference/commands',
        'cli-reference/options',
        'cli-reference/configuration',
      ],
    },
    {
      type: 'category',
      label: 'Templates',
      items: [
        'templates/overview',
        'templates/nestjs',
        'templates/nextjs',
        'templates/express',
        'templates/react',
        'templates/custom-templates',
      ],
    },
    {
      type: 'category',
      label: 'Frameworks',
      items: [
        'frameworks/nestjs',
        'frameworks/express',
        'frameworks/nextjs',
        'frameworks/react',
        'frameworks/vue',
        'frameworks/angular',
        'frameworks/svelte',
      ],
    },
    {
      type: 'category',
      label: 'Microservices',
      items: [
        'microservices/overview',
        'microservices/kafka',
        'microservices/grpc',
        'microservices/redis',
        'microservices/message-patterns',
      ],
    },
    {
      type: 'category',
      label: 'Database Integration',
      items: [
        'database/overview',
        'database/postgresql',
        'database/mysql',
        'database/mongodb',
        'database/sqlite',
      ],
    },
    {
      type: 'category',
      label: 'Styling',
      items: [
        'styling/tailwind-v4',
        'styling/styled-components',
        'styling/css-modules',
        'styling/sass',
      ],
    },
    {
      type: 'category',
      label: 'Deployment',
      items: [
        'deployment/docker',
        'deployment/kubernetes',
        'deployment/vercel',
        'deployment/aws',
      ],
    },
    {
      type: 'category',
      label: 'Examples',
      items: [
        'examples/ecommerce',
        'examples/blog-platform',
        'examples/chat-application',
        'examples/api-gateway',
      ],
    },
    {
      type: 'category',
      label: 'Contributing',
      items: [
        'contributing/overview',
        'contributing/development',
        'contributing/templates',
        'contributing/documentation',
      ],
    },
  ],
};

export default sidebars;
