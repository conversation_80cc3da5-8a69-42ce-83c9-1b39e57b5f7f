{"version": 3, "file": "eval.js", "sourceRoot": "", "sources": ["../../src/cli/eval.ts"], "names": [], "mappings": ";;;AAAA,kDAA6C;AAEtC,MAAM,UAAU,GAAG,KAAK,EAAE,MAAc,EAAE,EAAE;IACjD,MAAM,OAAO,GAAG,IAAI,0BAAW,EAAE,CAAA;IACjC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;IAC7C,OAAO,MAAM,CAAA;AACf,CAAC,CAAA;AAJY,QAAA,UAAU,cAItB", "sourcesContent": ["import { EdgeRuntime } from '../edge-runtime'\n\nexport const inlineEval = async (script: string) => {\n  const runtime = new EdgeRuntime()\n  const result = await runtime.evaluate(script)\n  return result\n}\n"]}