{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/cli/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,kDAA6C;AAC7C,+BAAgC;AAChC,2BAAiC;AACjC,sCAA6D;AAC7D,kEAAwC;AACxC,6CAAoC;AACpC,8CAAqB;AACrB,gDAAuB;AAEvB,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,KAAK,EAAE,GAAG,IAAA,aAAG,EAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;IACxD,KAAK,EAAE;QACL,CAAC,EAAE,MAAM;QACT,CAAC,EAAE,MAAM;QACT,CAAC,EAAE,QAAQ;QACX,CAAC,EAAE,MAAM;KACV;IACD,OAAO,EAAE;QACP,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE;QAClB,IAAI,EAAE,KAAK;QACX,IAAI,EAAE,KAAK;QACX,IAAI,EAAE,WAAW;QACjB,MAAM,EAAE,KAAK;QACb,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,KAAK;KACZ;CACF,CAAC,CAAA;AAEF,KAAK,UAAU,IAAI;IACjB,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;QACf,MAAM,EAAE,IAAI,EAAE,GAAG,wDAAa,QAAQ,GAAC,CAAA;QACvC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAA;QACnB,OAAM;IACR,CAAC;IAED,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;QACf,MAAM,EAAE,UAAU,EAAE,GAAG,wDAAa,QAAQ,GAAC,CAAA;QAC7C,OAAO,CAAC,GAAG,CAAC,MAAM,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACvC,OAAM;IACR,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,GAAG,KAAK,CAAA;IAE1B,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,MAAM,QAAQ,GAAG,cAAI,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,CAAA;QACnD,OAAO,IAAA,gBAAS,EAAC,uBAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE;YAClE,KAAK,EAAE,SAAS;SACjB,CAAC,CAAA;IACJ,CAAC;IAED,MAAM,WAAW,GAAG,IAAA,iBAAY,EAC9B,cAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,UAAU,CAAC,EACvC,OAAO,CACR,CAAA;IAED,MAAM,OAAO,GAAG,IAAI,0BAAW,CAAC,EAAE,WAAW,EAAE,CAAC,CAAA;IAChD,IAAI,CAAC,KAAK,CAAC,MAAM;QAAE,OAAO,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;IAE9C,MAAM,MAAM,GAAG,MAAM,kDAAO,UAAU,IAAE,IAAI,CAAC,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,CAChE,YAAY,EAAE,CACf,CAAA;IAED,MAAM,CAAC,KAAK,CACV,IAAI,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC,eAC/C,OAAO,CAAC,OACV,EAAE,CACH,CAAA;IAED;;OAEG;IACH,IAAI,MAAqC,CAAA;IACzC,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAA;IACrB,OAAO,MAAM,KAAK,SAAS,EAAE,CAAC;QAC5B,IAAI,CAAC;YACH,MAAM,GAAG,MAAM,IAAA,kBAAS,EAAC;gBACvB,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,MAAM,EAAE,MAAM;gBACd,IAAI;gBACJ,OAAO;aACR,CAAC,CAAA;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,IAAI,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,IAAI,MAAK,YAAY,EAAE,CAAC;gBACjC,MAAM,CAAC,IAAI,CAAC,UAAU,IAAI,mBAAmB,CAAC,CAAA;gBAC9C,EAAE,IAAI,CAAA;YACR,CAAC;;gBAAM,MAAM,KAAK,CAAA;QACpB,CAAC;IACH,CAAC;IAED,IAAA,oBAAM,EAAC,GAAG,EAAE,CAAC,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAK,EAAE,CAAC,CAAA;IAC7B,MAAM,CAAC,gCAAgC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;AACrE,CAAC;AAED,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,KAAU,EAAE,EAAE;IAC1B,IAAI,CAAC,CAAC,KAAK,YAAY,KAAK,CAAC;QAAE,KAAK,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,CAAA;IACvD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AACjB,CAAC,CAAC,CAAA", "sourcesContent": ["#!/usr/bin/env node\n\nimport { EdgeRuntime } from '../edge-runtime'\nimport { promisify } from 'util'\nimport { readFileSync } from 'fs'\nimport { runServer, type EdgeRuntimeServer } from '../server'\nimport childProcess from 'child_process'\nimport { onExit } from 'signal-exit'\nimport mri from 'mri'\nimport path from 'path'\n\nconst { _: input, ...flags } = mri(process.argv.slice(2), {\n  alias: {\n    e: 'eval',\n    h: 'host',\n    l: 'listen',\n    p: 'port',\n  },\n  default: {\n    cwd: process.cwd(),\n    eval: false,\n    help: false,\n    host: '127.0.0.1',\n    listen: false,\n    port: 3000,\n    repl: false,\n  },\n})\n\nasync function main() {\n  if (flags.help) {\n    const { help } = await import('./help')\n    console.log(help())\n    return\n  }\n\n  if (flags.eval) {\n    const { inlineEval } = await import('./eval')\n    console.log(await inlineEval(input[0]))\n    return\n  }\n\n  /**\n   * If there is no script path to run a server, the CLI will start a REPL.\n   */\n  const [scriptPath] = input\n\n  if (!scriptPath) {\n    const replPath = path.resolve(__dirname, 'repl.js')\n    return promisify(childProcess.spawn).call(null, 'node', [replPath], {\n      stdio: 'inherit',\n    })\n  }\n\n  const initialCode = readFileSync(\n    path.resolve(process.cwd(), scriptPath),\n    'utf-8',\n  )\n\n  const runtime = new EdgeRuntime({ initialCode })\n  if (!flags.listen) return runtime.evaluate('')\n\n  const logger = await import('./logger').then(({ createLogger }) =>\n    createLogger(),\n  )\n\n  logger.debug(\n    `v${String(require('../../package.json').version)} at Node.js ${\n      process.version\n    }`,\n  )\n\n  /**\n   * Start a server with the script provided in the file path.\n   */\n  let server: undefined | EdgeRuntimeServer\n  let port = flags.port\n  while (server === undefined) {\n    try {\n      server = await runServer({\n        host: flags.host,\n        logger: logger,\n        port,\n        runtime,\n      })\n    } catch (error: any) {\n      if (error?.code === 'EADDRINUSE') {\n        logger.warn(`Port \\`${port}\\` already in use`)\n        ++port\n      } else throw error\n    }\n  }\n\n  onExit(() => server?.close())\n  logger(`Waiting incoming requests at ${logger.quotes(server.url)}`)\n}\n\nmain().catch((error: any) => {\n  if (!(error instanceof Error)) error = new Error(error)\n  process.exit(1)\n})\n"]}