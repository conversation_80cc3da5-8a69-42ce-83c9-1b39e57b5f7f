{"version": 3, "file": "body-streams.js", "sourceRoot": "", "sources": ["../../src/server/body-streams.ts"], "names": [], "mappings": ";;;AAEA,mCAAiC;AAIjC;;;GAGG;AACH,SAAgB,qBAAqB,CACnC,eAAkB,EAClB,WAA8B,EAC9B,gBAAwC;IAExC,IAAI,kBAAkB,GAAsB,IAAI,CAAA;IAEhD,OAAO;QACL;;;;WAIG;QACH,QAAQ;YACN,IAAI,kBAAkB,EAAE,CAAC;gBACvB,kBAAkB,CAChB,eAAe,EACf,sBAAsB,CAAC,kBAAkB,CAAC,CAC3C,CAAA;YACH,CAAC;QACH,CAAC;QACD;;;WAGG;QACH,eAAe;YACb,MAAM,cAAc,GAClB,kBAAkB,aAAlB,kBAAkB,cAAlB,kBAAkB,GAClB,mBAAmB,CAAC,eAAe,EAAE,WAAW,EAAE,gBAAgB,CAAC,CAAA;YACrE,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,cAAc,CAAC,GAAG,EAAE,CAAA;YAC/C,kBAAkB,GAAG,OAAO,CAAA;YAC5B,OAAO,OAAO,CAAA;QAChB,CAAC;KACF,CAAA;AACH,CAAC;AAlCD,sDAkCC;AAED;;GAEG;AACH,SAAS,mBAAmB,CAC1B,OAAwB,EACxB,WAA8B,EAC9B,gBAAwC;IAExC,MAAM,SAAS,GAAG,IAAI,gBAAgB,CAAyB;QAC7D,KAAK,CAAC,UAAU;YACd,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE,CAC3B,UAAU,CAAC,OAAO,CAAC,IAAI,WAAW,CAAC,CAAC,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAChE,CAAA;YACD,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC,CAAA;YAC/C,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAA;QACrD,CAAC;KACF,CAAC,CAAA;IAEF,OAAO,SAAS,CAAC,QAAiD,CAAA;AACpE,CAAC;AAED,SAAS,sBAAsB,CAAC,UAAsB;IACpD,MAAM,MAAM,GAAG,UAAU,CAAC,SAAS,EAAE,CAAA;IACrC,OAAO,iBAAQ,CAAC,IAAI,CAClB,CAAC,KAAK,SAAS,CAAC;QACd,OAAO,IAAI,EAAE,CAAC;YACZ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAA;YAC3C,IAAI,IAAI,EAAE,CAAC;gBACT,OAAM;YACR,CAAC;YACD,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC,CAAC,EAAE,CACL,CAAA;AACH,CAAC;AAED,SAAS,kBAAkB,CACzB,IAAO,EACP,MAAgB;IAEhB,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;QACzB,IAAI,CAAC,GAAG,MAAM,CAAC,GAAqB,CAAQ,CAAA;QAC5C,IAAI,OAAO,CAAC,KAAK,UAAU,EAAE,CAAC;YAC5B,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QACpB,CAAC;QACD,IAAI,CAAC,GAAc,CAAC,GAAG,CAAC,CAAA;IAC1B,CAAC;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAED,SAAS,iBAAiB,CAAC,KAAU;;IACnC,OAAO,CAAA,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,WAAW,0CAAE,IAAI,KAAI,YAAY,CAAA;AACjD,CAAC;AAED;;;;GAIG;AACI,KAAK,SAAS,CAAC,CAAC,+BAA+B,CAAC,IAAqB;IAC1E,MAAM,MAAM,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,SAAS,EAAE,CAAA;IAChC,IAAI,MAAM,EAAE,CAAC;QACX,IAAI,KAAK,CAAA;QACT,IAAI,CAAC;YACH,OAAO,IAAI,EAAE,CAAC;gBACZ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAA;gBAC3C,IAAI,IAAI,EAAE,CAAC;oBACT,OAAM;gBACR,CAAC;gBAED,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC9B,KAAK,GAAG,IAAI,SAAS,CAAC,2CAA2C,CAAC,CAAA;oBAClE,MAAK;gBACP,CAAC;gBACD,MAAM,KAAK,CAAA;YACb,CAAC;QACH,CAAC;gBAAS,CAAC;YACT,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;gBACpB,MAAM,KAAK,CAAA;YACb,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,MAAM,EAAE,CAAA;YACjB,CAAC;QACH,CAAC;IACH,CAAC;AACH,CAAC;AA1BD,0EA0BC;AAED;;;;GAIG;AACI,KAAK,UAAU,wBAAwB,CAC5C,IAAuB,EACvB,GAAa;IAEb,IAAI,CAAC,IAAI;QAAE,OAAM;IAEjB,+EAA+E;IAC/E,IAAI,GAAG,CAAC,SAAS;QAAE,OAAO,IAAI,CAAC,MAAM,EAAE,CAAA;IAEvC,0EAA0E;IAC1E,6EAA6E;IAC7E,2EAA2E;IAC3E,SAAS;IACT,IAAI,YAAwB,CAAA;IAC5B,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,YAAY,aAAZ,YAAY,uBAAZ,YAAY,EAAI,CAAC,CAAA;IAEvC,kEAAkE;IAClE,2DAA2D;IAC3D,IAAI,IAAI,GAAG,IAAI,CAAA;IACf,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;QACnB,IAAI,GAAG,KAAK,CAAA;QACZ,YAAY,aAAZ,YAAY,uBAAZ,YAAY,EAAI,CAAA;IAClB,CAAC,CAAC,CAAA;IAEF,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;IAC/B,OAAO,IAAI,EAAE,CAAC;QACZ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAA;QAC3C,IAAI,IAAI;YAAE,MAAK;QAEf,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9B,MAAM,KAAK,GAAG,IAAI,SAAS,CAAC,2CAA2C,CAAC,CAAA;YACxE,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;YACpB,MAAM,KAAK,CAAA;QACb,CAAC;QAED,IAAI,IAAI,EAAE,CAAC;YACT,MAAM,oBAAoB,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YAE7C,sEAAsE;YACtE,4CAA4C;YAC5C,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC1B,MAAM,IAAI,OAAO,CAAO,CAAC,GAAG,EAAE,EAAE;oBAC9B,YAAY,GAAG,GAAG,CAAA;gBACpB,CAAC,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,wEAAwE;QACxE,4EAA4E;QAC5E,sDAAsD;QACtD,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO,MAAM,CAAC,MAAM,EAAE,CAAA;QACxB,CAAC;IACH,CAAC;AACH,CAAC;AAtDD,4DAsDC", "sourcesContent": ["import type { IncomingMessage } from 'http'\nimport type { Writable } from 'stream'\nimport { Readable } from 'stream'\n\ntype BodyStream = ReadableStream<Uint8Array>\n\n/**\n * An interface that encapsulates body stream cloning\n * of an incoming request.\n */\nexport function getClonableBodyStream<T extends IncomingMessage>(\n  incomingMessage: T,\n  KUint8Array: typeof Uint8Array,\n  KTransformStream: typeof TransformStream,\n) {\n  let bufferedBodyStream: BodyStream | null = null\n\n  return {\n    /**\n     * Replaces the original request body if necessary.\n     * This is done because once we read the body from the original request,\n     * we can't read it again.\n     */\n    finalize(): void {\n      if (bufferedBodyStream) {\n        replaceRequestBody(\n          incomingMessage,\n          bodyStreamToNodeStream(bufferedBodyStream),\n        )\n      }\n    },\n    /**\n     * Clones the body stream\n     * to pass into a middleware\n     */\n    cloneBodyStream(): BodyStream {\n      const originalStream =\n        bufferedBodyStream ??\n        requestToBodyStream(incomingMessage, KUint8Array, KTransformStream)\n      const [stream1, stream2] = originalStream.tee()\n      bufferedBodyStream = stream1\n      return stream2\n    },\n  }\n}\n\n/**\n * Creates a ReadableStream from a Node.js HTTP request\n */\nfunction requestToBodyStream(\n  request: IncomingMessage,\n  KUint8Array: typeof Uint8Array,\n  KTransformStream: typeof TransformStream,\n): BodyStream {\n  const transform = new KTransformStream<Uint8Array, Uint8Array>({\n    start(controller) {\n      request.on('data', (chunk) =>\n        controller.enqueue(new KUint8Array([...new Uint8Array(chunk)])),\n      )\n      request.on('end', () => controller.terminate())\n      request.on('error', (err) => controller.error(err))\n    },\n  })\n\n  return transform.readable as unknown as ReadableStream<Uint8Array>\n}\n\nfunction bodyStreamToNodeStream(bodyStream: BodyStream): Readable {\n  const reader = bodyStream.getReader()\n  return Readable.from(\n    (async function* () {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) {\n          return\n        }\n        yield value\n      }\n    })(),\n  )\n}\n\nfunction replaceRequestBody<T extends IncomingMessage>(\n  base: T,\n  stream: Readable,\n): T {\n  for (const key in stream) {\n    let v = stream[key as keyof Readable] as any\n    if (typeof v === 'function') {\n      v = v.bind(stream)\n    }\n    base[key as keyof T] = v\n  }\n\n  return base\n}\n\nfunction isUint8ArrayChunk(value: any): value is Uint8Array {\n  return value?.constructor?.name == 'Uint8Array'\n}\n\n/**\n * Creates an async iterator from a ReadableStream that ensures that every\n * emitted chunk is a `Uint8Array`. If there is some invalid chunk it will\n * throw.\n */\nexport async function* consumeUint8ArrayReadableStream(body?: ReadableStream) {\n  const reader = body?.getReader()\n  if (reader) {\n    let error\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) {\n          return\n        }\n\n        if (!isUint8ArrayChunk(value)) {\n          error = new TypeError('This ReadableStream did not return bytes.')\n          break\n        }\n        yield value\n      }\n    } finally {\n      if (error) {\n        reader.cancel(error)\n        throw error\n      } else {\n        reader.cancel()\n      }\n    }\n  }\n}\n\n/**\n * Pipes the chunks of a BodyStream into a Response. This optimizes for\n * laziness, pauses reading if we experience back-pressure, and handles early\n * disconnects by the client on the other end of the server response.\n */\nexport async function pipeBodyStreamToResponse(\n  body: BodyStream | null,\n  res: Writable,\n) {\n  if (!body) return\n\n  // If the client has already disconnected, then we don't need to pipe anything.\n  if (res.destroyed) return body.cancel()\n\n  // When the server pushes more data than the client reads, then we need to\n  // wait for the client to catch up before writing more data. We register this\n  // generic handler once so that we don't incur constant register/unregister\n  // calls.\n  let drainResolve: () => void\n  res.on('drain', () => drainResolve?.())\n\n  // If the user aborts, then we'll receive a close event before the\n  // body closes. In that case, we want to end the streaming.\n  let open = true\n  res.on('close', () => {\n    open = false\n    drainResolve?.()\n  })\n\n  const reader = body.getReader()\n  while (open) {\n    const { done, value } = await reader.read()\n    if (done) break\n\n    if (!isUint8ArrayChunk(value)) {\n      const error = new TypeError('This ReadableStream did not return bytes.')\n      reader.cancel(error)\n      throw error\n    }\n\n    if (open) {\n      const bufferSpaceAvailable = res.write(value)\n\n      // If there's no more space in the buffer, then we need to wait on the\n      // client to read data before pushing again.\n      if (!bufferSpaceAvailable) {\n        await new Promise<void>((res) => {\n          drainResolve = res\n        })\n      }\n    }\n\n    // If the client disconnected early, then we need to cleanup the stream.\n    // This cannot be joined with the above if-statement, because the client may\n    // have disconnected while waiting for a drain signal.\n    if (!open) {\n      return reader.cancel()\n    }\n  }\n}\n"]}