{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../src/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { Colors } from 'picocolors/types'\n\nexport interface LoggerOptions {\n  color?: keyof Colors\n  withHeader?: boolean\n  withBreakline?: boolean\n}\n\nexport interface Logger {\n  (message: string, opts?: LoggerOptions): void\n  warn(message: string, opts?: LoggerOptions): void\n  debug(message: string, opts?: LoggerOptions): void\n  error(message: string, opts?: LoggerOptions): void\n  info(message: string, opts?: LoggerOptions): void\n  quotes(str: string): string\n}\n\nexport interface NodeHeaders {\n  [header: string]: string | string[] | undefined\n}\n"]}