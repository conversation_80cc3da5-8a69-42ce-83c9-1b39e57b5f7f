{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;AAAA,mCAKiB;AAJf,yHAAA,+BAA+B,OAAA;AAC/B,kHAAA,wBAAwB,OAAA;AACxB,uGAAA,aAAa,OAAA;AACb,mGAAA,SAAS,OAAA;AAGX,+CAA4C;AAAnC,2GAAA,WAAW,OAAA", "sourcesContent": ["export {\n  consumeUint8ArrayReadableStream,\n  pipeBodyStreamToResponse,\n  createHandler,\n  runServer,\n} from './server'\n\nexport { EdgeRuntime } from './edge-runtime'\n"]}