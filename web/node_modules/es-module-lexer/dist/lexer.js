/* es-module-lexer 1.4.1 */
const A=1===new Uint8Array(new Uint16Array([1]).buffer)[0];export function parse(E,g="@"){if(!C)return init.then((()=>parse(E)));const I=E.length+1,k=(C.__heap_base.value||C.__heap_base)+4*I-C.memory.buffer.byteLength;k>0&&C.memory.grow(Math.ceil(k/65536));const K=C.sa(I-1);if((A?B:Q)(E,new Uint16Array(C.memory.buffer,K,I)),!C.parse())throw Object.assign(new Error(`Parse error ${g}:${E.slice(0,C.e()).split("\n").length}:${C.e()-E.lastIndexOf("\n",C.e()-1)}`),{idx:C.e()});const o=[],D=[];for(;C.ri();){const A=C.is(),Q=C.ie(),B=C.ai(),g=C.id(),I=C.ss(),k=C.se();let K;C.ip()&&(K=w(E.slice(-1===g?A-1:A,-1===g?Q+1:Q))),o.push({n:K,s:A,e:Q,ss:I,se:k,d:g,a:B})}for(;C.re();){const A=C.es(),Q=C.ee(),B=C.els(),g=C.ele(),I=E.slice(A,Q),k=I[0],K=B<0?void 0:E.slice(B,g),o=K?K[0]:"";D.push({s:A,e:Q,ls:B,le:g,n:'"'===k||"'"===k?w(I):I,ln:'"'===o||"'"===o?w(K):K})}function w(A){try{return(0,eval)(A)}catch(A){}}return[o,D,!!C.f(),!!C.ms()]}function Q(A,Q){const B=A.length;let C=0;for(;C<B;){const B=A.charCodeAt(C);Q[C++]=(255&B)<<8|B>>>8}}function B(A,Q){const B=A.length;let C=0;for(;C<B;)Q[C]=A.charCodeAt(C++)}let C;export const init=WebAssembly.compile((E="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","undefined"!=typeof Buffer?Buffer.from(E,"base64"):Uint8Array.from(atob(E),(A=>A.charCodeAt(0))))).then(WebAssembly.instantiate).then((({exports:A})=>{C=A}));var E;