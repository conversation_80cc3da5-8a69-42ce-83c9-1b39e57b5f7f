!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).jsonExt=t()}(this,(function(){"use strict";function e(e){return"function"==typeof e.pipe&&"function"==typeof e._read&&"object"==typeof e._readableState&&null!==e._readableState}var t={escapableCharCodeSubstitution:{8:"\\b",9:"\\t",10:"\\n",12:"\\f",13:"\\r",34:'\\"',92:"\\\\"},isLeadingSurrogate:function(e){return e>=55296&&e<=56319},isTrailingSurrogate:function(e){return e>=56320&&e<=57343},type:{PRIMITIVE:1,PROMISE:4,ARRAY:3,OBJECT:2,STRING_STREAM:5,OBJECT_STREAM:6},isReadableStream:e,replaceValue:function(e,t,s,n){switch(s&&"function"==typeof s.toJSON&&(s=s.toJSON()),null!==n&&(s=n.call(e,String(t),s)),typeof s){case"function":case"symbol":s=void 0;break;case"object":if(null!==s){const e=s.constructor;e!==String&&e!==Number&&e!==Boolean||(s=s.valueOf())}}return s},getTypeNative:function(e){return null===e||"object"!=typeof e?1:Array.isArray(e)?3:2},getTypeAsync:function(t){return null===t||"object"!=typeof t?1:"function"==typeof t.then?4:e(t)?t._readableState.objectMode?6:5:Array.isArray(t)?3:2},normalizeReplacer:function(e){return"function"==typeof e?e:Array.isArray(e)?[...new Set(e.map((e=>{const t=e&&e.constructor;return t===String||t===Number?String(e):null})).filter((e=>"string"==typeof e)))]:null},normalizeSpace:function(e){return"number"==typeof e?!(!Number.isFinite(e)||e<1)&&" ".repeat(Math.min(e,10)):"string"==typeof e&&e.slice(0,10)||!1}};const{normalizeReplacer:s,normalizeSpace:n,replaceValue:i,getTypeNative:r,getTypeAsync:a,isLeadingSurrogate:l,isTrailingSurrogate:h,escapableCharCodeSubstitution:u,type:{PRIMITIVE:o,OBJECT:c,ARRAY:f,PROMISE:p,STRING_STREAM:d,OBJECT_STREAM:g}}=t,y=Array.from({length:2048}).map(((e,t)=>u.hasOwnProperty(t)?2:t<32?6:t<128?1:2));function S(e){let t=0,s=!1;for(let n=0;n<e.length;n++){const i=e.charCodeAt(n);if(i<2048)t+=y[i];else{if(l(i)){t+=6,s=!0;continue}h(i)?t=s?t-2:t+6:t+=3}s=!1}return t+2}var b=TextDecoder;const{isReadableStream:k}=t,A=new b;function v(e){return null!==e&&"object"==typeof e}function m(e,t){return"SyntaxError"===e.name&&t.jsonParseOffset&&(e.message=e.message.replace(/at position (\d+)/,((e,s)=>"at position "+(Number(s)+t.jsonParseOffset)))),e}class O{constructor(){this.value=void 0,this.valueStack=null,this.stack=new Array(100),this.lastFlushDepth=0,this.flushDepth=0,this.stateString=!1,this.stateStringEscape=!1,this.pendingByteSeq=null,this.pendingChunk=null,this.chunkOffset=0,this.jsonParseOffset=0}parseAndAppend(e,t){1===this.stack[this.lastFlushDepth-1]?(t&&(this.jsonParseOffset--,e="{"+e+"}"),Object.assign(this.valueStack.value,JSON.parse(e))):(t&&(this.jsonParseOffset--,e="["+e+"]"),function(e,t){const s=e.length;e.length+=t.length;for(let n=0;n<t.length;n++)e[s+n]=t[n]}(this.valueStack.value,JSON.parse(e)))}prepareAddition(e){const{value:t}=this.valueStack;if(Array.isArray(t)?0!==t.length:0!==Object.keys(t).length){if(","===e[0])return this.jsonParseOffset++,e.slice(1);if("}"!==e[0]&&"]"!==e[0])return this.jsonParseOffset-=3,"[[]"+e}return e}flush(e,t,s){let n=e.slice(t,s);if(this.jsonParseOffset=this.chunkOffset+t,null!==this.pendingChunk&&(n=this.pendingChunk+n,this.jsonParseOffset-=this.pendingChunk.length,this.pendingChunk=null),this.flushDepth===this.lastFlushDepth)this.flushDepth>0?this.parseAndAppend(this.prepareAddition(n),!0):(this.value=JSON.parse(n),this.valueStack={value:this.value,prev:null});else if(this.flushDepth>this.lastFlushDepth){for(let e=this.flushDepth-1;e>=this.lastFlushDepth;e--)n+=1===this.stack[e]?"}":"]";0===this.lastFlushDepth?(this.value=JSON.parse(n),this.valueStack={value:this.value,prev:null}):this.parseAndAppend(this.prepareAddition(n),!0);for(let e=this.lastFlushDepth||1;e<this.flushDepth;e++){let t=this.valueStack.value;if(1===this.stack[e-1]){let e;for(e in t);t=t[e]}else t=t[t.length-1];this.valueStack={value:t,prev:this.valueStack}}}else{n=this.prepareAddition(n);for(let e=this.lastFlushDepth-1;e>=this.flushDepth;e--)this.jsonParseOffset--,n=(1===this.stack[e]?"{":"[")+n;this.parseAndAppend(n,!1);for(let e=this.lastFlushDepth-1;e>=this.flushDepth;e--)this.valueStack=this.valueStack.prev}this.lastFlushDepth=this.flushDepth}push(e){if("string"!=typeof e){if(null!==this.pendingByteSeq){const t=e;(e=new Uint8Array(this.pendingByteSeq.length+t.length)).set(this.pendingByteSeq),e.set(t,this.pendingByteSeq.length),this.pendingByteSeq=null}if(e[e.length-1]>127)for(let t=0;t<e.length;t++){const s=e[e.length-1-t];if(s>>6==3){t++,(4!==t&&s>>3==30||3!==t&&s>>4==14||2!==t&&s>>5==6)&&(this.pendingByteSeq=e.slice(e.length-t),e=e.slice(0,-t));break}}e=A.decode(e)}const t=e.length;let s=0,n=0;e:for(let i=0;i<t;i++){if(this.stateString){for(;i<t;i++)if(this.stateStringEscape)this.stateStringEscape=!1;else switch(e.charCodeAt(i)){case 34:this.stateString=!1;continue e;case 92:this.stateStringEscape=!0}break}switch(e.charCodeAt(i)){case 34:this.stateString=!0,this.stateStringEscape=!1;break;case 44:n=i;break;case 123:n=i+1,this.stack[this.flushDepth++]=1;break;case 91:n=i+1,this.stack[this.flushDepth++]=2;break;case 93:case 125:n=i+1,this.flushDepth--,this.flushDepth<this.lastFlushDepth&&(this.flush(e,s,n),s=n);break;case 9:case 10:case 13:case 32:s===i&&s++,n===i&&n++}}n>s&&this.flush(e,s,n),n<t&&(null!==this.pendingChunk?this.pendingChunk+=e:this.pendingChunk=e.slice(n,t)),this.chunkOffset+=t}finish(){return null!==this.pendingChunk&&(this.flush("",0,0),this.pendingChunk=null),this.value}}return{version:"0.5.7",stringifyInfo:function(e,t,l,h){let u=null;t=s(t),Array.isArray(t)&&(u=new Set(t),t=null),l=function(e){return"string"==typeof(e=n(e))?e.length:0}(l),h=h||{};const y=new Map,b=new Set,k=new Set,A=new Set,v=new Set,m=h.async?a:r,O={"":e};let w=!1,D=0;return function e(s,n,r){if(w)return;r=i(s,n,r,t);let a=m(r);if(a!==o&&b.has(r))return A.add(r),D+=4,void(h.continueOnCircular||(w=!0));switch(a){case o:void 0!==r||Array.isArray(s)?D+=function(e){switch(typeof e){case"string":return S(e);case"number":return Number.isFinite(e)?String(e).length:4;case"boolean":return e?4:5;case"undefined":case"object":return 4;default:return 0}}(r):s===O&&(D+=9);break;case c:{if(y.has(r)){k.add(r),D+=y.get(r);break}const t=D;let s=0;D+=2,b.add(r);for(const t in r)if(hasOwnProperty.call(r,t)&&(null===u||u.has(t))){const n=D;e(r,t,r[t]),n!==D&&(D+=S(t)+1,s++)}s>1&&(D+=s-1),b.delete(r),l>0&&s>0&&(D+=(1+(b.size+1)*l+1)*s,D+=1+b.size*l),y.set(r,D-t);break}case f:{if(y.has(r)){k.add(r),D+=y.get(r);break}const t=D;D+=2,b.add(r);for(let t=0;t<r.length;t++)e(r,t,r[t]);r.length>1&&(D+=r.length-1),b.delete(r),l>0&&r.length>0&&(D+=(1+(b.size+1)*l)*r.length,D+=1+b.size*l),y.set(r,D-t);break}case p:case d:v.add(r);break;case g:D+=2,v.add(r)}}(O,"",e),{minLength:isNaN(D)?1/0:D,circular:[...A],duplicate:[...k],async:[...v]}},stringifyStream:()=>{throw new Error("Method is not supported")},parseChunked:function(e){let t=new O;if(v(e)&&k(e))return new Promise(((s,n)=>{e.on("data",(e=>{try{t.push(e)}catch(e){n(m(e,t)),t=null}})).on("error",(e=>{t=null,n(e)})).on("end",(()=>{try{s(t.finish())}catch(e){n(m(e,t))}finally{t=null}}))}));if("function"==typeof e){const s=e();if(v(s)&&(Symbol.iterator in s||Symbol.asyncIterator in s))return new Promise((async(e,n)=>{try{for await(const e of s)t.push(e);e(t.finish())}catch(e){n(m(e,t))}finally{t=null}}))}throw new Error("Chunk emitter should be readable stream, generator, async generator or function returning an iterable object")}}}));
