{"name": "@vercel/remix-builder", "version": "5.4.9", "license": "Apache-2.0", "main": "./dist/index.js", "homepage": "https://vercel.com/docs", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/vercel/vercel.git", "directory": "packages/remix"}, "files": ["dist", "defaults"], "dependencies": {"@vercel/error-utils": "2.0.3", "@vercel/nft": "0.29.2", "@vercel/static-config": "3.1.1", "path-to-regexp-updated": "npm:path-to-regexp@6.3.0", "path-to-regexp": "6.1.0", "ts-morph": "12.0.0"}, "devDependencies": {"@types/jest": "27.5.1", "@types/node": "14.18.33", "@types/semver": "7.3.13", "@vercel/build-utils": "10.6.0", "glob": "10.3.16", "jest-junit": "16.0.0", "semver": "7.5.2", "vitest": "2.0.1"}, "VERCEL_REMIX_RUN_DEV_MAX_VERSION": "2.16.6", "scripts": {"build": "node ../../utils/build-builder.mjs", "test": "jest --reporters=default --reporters=jest-junit --env node --verbose --bail --runInBand", "vitest-run": "vitest -c ../../vitest.config.mts", "vitest-unit": "glob --absolute 'test/unit.*.test.ts'", "vitest-e2e": "glob --absolute 'test/integration-*.test.ts'", "type-check": "tsc --noEmit"}}