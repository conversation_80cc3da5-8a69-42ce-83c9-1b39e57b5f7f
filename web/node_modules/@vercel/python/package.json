{"name": "@vercel/python", "version": "4.7.2", "main": "./dist/index.js", "license": "Apache-2.0", "homepage": "https://vercel.com/docs/runtimes#official-runtimes/python", "files": ["dist", "vc_init.py"], "repository": {"type": "git", "url": "https://github.com/vercel/vercel.git", "directory": "packages/python"}, "devDependencies": {"@types/execa": "^0.9.0", "@types/fs-extra": "11.0.2", "@types/jest": "27.4.1", "@types/node": "14.18.33", "@types/which": "3.0.0", "@vercel/build-utils": "10.5.1", "cross-env": "7.0.3", "execa": "^1.0.0", "fs-extra": "11.1.1", "jest-junit": "16.0.0", "which": "3.0.0"}, "scripts": {"build": "node ../../utils/build-builder.mjs", "test": "cross-env VERCEL_FORCE_PYTHON_STREAMING=1 jest --reporters=default --reporters=jest-junit --env node --verbose --runInBand --bail", "test-unit": "pnpm test test/unit.test.ts", "test-e2e": "pnpm test test/integration-*", "type-check": "tsc --noEmit"}}