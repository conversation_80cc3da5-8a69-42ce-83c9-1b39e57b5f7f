{"version": 3, "file": "tsconfig-schema.js", "sourceRoot": "", "sources": ["../src/tsconfig-schema.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { TsConfigOptions } from './index';\n\n/*\n * This interface exists solely for generating a JSON schema for tsconfig.json.\n * We do *not* extend the compiler's tsconfig interface.  Instead we handle that\n * on a schema level, via \"allOf\", so we pull in the same schema that VSCode\n * already uses.\n */\n/**\n * tsconfig schema which includes \"ts-node\" options.\n * @allOf [{\"$ref\": \"https://schemastore.azurewebsites.net/schemas/json/tsconfig.json\"}]\n */\nexport interface TsConfigSchema {\n  /**\n   * ts-node options.  See also: https://typestrong.org/ts-node/docs/configuration\n   *\n   * ts-node offers TypeScript execution and REPL for node.js, with source map support.\n   */\n  'ts-node': TsConfigOptions;\n}\n"]}