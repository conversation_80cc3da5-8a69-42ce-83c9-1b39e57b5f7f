{"name": "@vercel/node", "version": "5.3.5", "license": "Apache-2.0", "main": "./dist/index", "homepage": "https://vercel.com/docs/runtimes#official-runtimes/node-js", "repository": {"type": "git", "url": "https://github.com/vercel/vercel.git", "directory": "packages/node"}, "files": ["dist"], "dependencies": {"@edge-runtime/node-utils": "2.3.0", "@edge-runtime/primitives": "4.1.0", "@edge-runtime/vm": "3.2.0", "@types/node": "16.18.11", "@vercel/build-utils": "10.6.6", "@vercel/error-utils": "2.0.3", "@vercel/nft": "0.29.2", "@vercel/static-config": "3.1.1", "async-listen": "3.0.0", "cjs-module-lexer": "1.2.3", "edge-runtime": "2.5.9", "es-module-lexer": "1.4.1", "esbuild": "0.14.47", "etag": "1.8.1", "node-fetch": "2.6.9", "path-to-regexp-updated": "npm:path-to-regexp@6.3.0", "path-to-regexp": "6.1.0", "ts-morph": "12.0.0", "ts-node": "10.9.1", "typescript": "4.9.5", "undici": "5.28.4"}, "devDependencies": {"@babel/core": "7.24.4", "@babel/plugin-transform-modules-commonjs": "7.24.1", "@babel/preset-typescript": "7.26.0", "@tootallnate/once": "1.1.2", "@types/aws-lambda": "8.10.19", "@types/content-type": "1.1.5", "@types/cookie": "0.3.3", "@types/etag": "1.8.0", "@types/jest": "29.5.0", "@vitest/expect": "1.4.0", "content-type": "1.0.5", "cookie": "0.7.0", "cross-env": "7.0.3", "execa": "3.2.0", "fs-extra": "11.1.0", "glob": "10.3.16", "jest-junit": "16.0.0", "source-map-support": "0.5.12", "tree-kill": "1.2.2", "vite": "^5.1.6", "vitest": "^2.0.1", "@vercel/functions": "2.2.3"}, "scripts": {"build": "node build.mjs", "test": "cross-env NODE_OPTIONS=--experimental-vm-modules jest --env node --verbose --bail --runInBand", "test-e2e": "pnpm test test/integration", "type-check": "tsc --noEmit", "vitest-run": "vitest -c ../../vitest.config.mts", "vitest-unit": "glob --absolute 'test/unit/**/*.test.ts' 'test/unit/**/*.test.mts'"}}