{"version": 3, "file": "keyword.js", "sourceRoot": "", "sources": ["../../../lib/compile/validate/keyword.ts"], "names": [], "mappings": ";;;AAUA,wCAAsE;AACtE,oCAAwB;AAExB,kDAAwD;AACxD,sCAAsC;AAItC,SAAgB,gBAAgB,CAAC,GAAe,EAAE,GAA2B;IAC3E,MAAM,EAAC,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,EAAC,GAAG,GAAG,CAAA;IACpD,MAAM,WAAW,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,CAAC,CAAA;IACrE,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE,OAAO,EAAE,WAAW,CAAC,CAAA;IACvD,IAAI,EAAE,CAAC,IAAI,CAAC,cAAc,KAAK,KAAK;QAAE,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,IAAI,CAAC,CAAA;IAE/E,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;IAC/B,GAAG,CAAC,SAAS,CACX;QACE,MAAM,EAAE,WAAW;QACnB,UAAU,EAAE,aAAG;QACf,aAAa,EAAE,GAAG,EAAE,CAAC,aAAa,IAAI,OAAO,EAAE;QAC/C,YAAY,EAAE,SAAS;QACvB,aAAa,EAAE,IAAI;KACpB,EACD,KAAK,CACN,CAAA;IACD,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;AACxC,CAAC;AAlBD,4CAkBC;AAED,SAAgB,eAAe,CAAC,GAAe,EAAE,GAA0B;;IACzE,MAAM,EAAC,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,EAAE,EAAC,GAAG,GAAG,CAAA;IAC3D,iBAAiB,CAAC,EAAE,EAAE,GAAG,CAAC,CAAA;IAC1B,MAAM,QAAQ,GACZ,CAAC,KAAK,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAA;IAC5F,MAAM,WAAW,GAAG,UAAU,CAAC,GAAG,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAA;IACtD,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;IAC9B,GAAG,CAAC,UAAU,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;IACtC,GAAG,CAAC,EAAE,CAAC,MAAA,GAAG,CAAC,KAAK,mCAAI,KAAK,CAAC,CAAA;IAE1B,SAAS,eAAe;QACtB,IAAI,GAAG,CAAC,MAAM,KAAK,KAAK,EAAE;YACxB,WAAW,EAAE,CAAA;YACb,IAAI,GAAG,CAAC,SAAS;gBAAE,UAAU,CAAC,GAAG,CAAC,CAAA;YAClC,UAAU,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAA;SAC9B;aAAM;YACL,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,YAAY,EAAE,CAAA;YAC7D,IAAI,GAAG,CAAC,SAAS;gBAAE,UAAU,CAAC,GAAG,CAAC,CAAA;YAClC,UAAU,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAA;SACzC;IACH,CAAC;IAED,SAAS,aAAa;QACpB,MAAM,QAAQ,GAAG,GAAG,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAA;QAC1C,GAAG,CAAC,GAAG,CACL,GAAG,EAAE,CAAC,WAAW,CAAC,IAAA,WAAC,EAAA,QAAQ,CAAC,EAC5B,CAAC,CAAC,EAAE,EAAE,CACJ,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,CACzB,IAAA,WAAC,EAAA,GAAG,CAAC,eAAe,EAAE,CAAC,eAAuB,EAAE,EAChD,GAAG,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAA,WAAC,EAAA,GAAG,CAAC,SAAS,CAAC,EAC1C,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CACnB,CACJ,CAAA;QACD,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED,SAAS,YAAY;QACnB,MAAM,YAAY,GAAG,IAAA,WAAC,EAAA,GAAG,WAAW,SAAS,CAAA;QAC7C,GAAG,CAAC,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC,CAAA;QAC9B,WAAW,CAAC,aAAG,CAAC,CAAA;QAChB,OAAO,YAAY,CAAA;IACrB,CAAC;IAED,SAAS,WAAW,CAAC,SAAe,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,IAAA,WAAC,EAAA,QAAQ,CAAC,CAAC,CAAC,aAAG;QAC7D,MAAM,OAAO,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,eAAC,CAAC,IAAI,CAAC,CAAC,CAAC,eAAC,CAAC,IAAI,CAAA;QACrD,MAAM,UAAU,GAAG,CAAC,CAAC,CAAC,SAAS,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,MAAM,KAAK,KAAK,CAAC,CAAA;QAC1E,GAAG,CAAC,MAAM,CACR,KAAK,EACL,IAAA,WAAC,EAAA,GAAG,MAAM,GAAG,IAAA,uBAAgB,EAAC,GAAG,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,CAAC,EAAE,EACtE,GAAG,CAAC,SAAS,CACd,CAAA;IACH,CAAC;IAED,SAAS,UAAU,CAAC,MAAkB;;QACpC,GAAG,CAAC,EAAE,CAAC,IAAA,aAAG,EAAC,MAAA,GAAG,CAAC,KAAK,mCAAI,KAAK,CAAC,EAAE,MAAM,CAAC,CAAA;IACzC,CAAC;AACH,CAAC;AAxDD,0CAwDC;AAED,SAAS,UAAU,CAAC,GAAe;IACjC,MAAM,EAAC,GAAG,EAAE,IAAI,EAAE,EAAE,EAAC,GAAG,GAAG,CAAA;IAC3B,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,UAAU,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,IAAA,WAAC,EAAA,GAAG,EAAE,CAAC,UAAU,IAAI,EAAE,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAA;AAC9F,CAAC;AAED,SAAS,OAAO,CAAC,GAAe,EAAE,IAAU;IAC1C,MAAM,EAAC,GAAG,EAAC,GAAG,GAAG,CAAA;IACjB,GAAG,CAAC,EAAE,CACJ,IAAA,WAAC,EAAA,iBAAiB,IAAI,GAAG,EACzB,GAAG,EAAE;QACH,GAAG;aACA,MAAM,CAAC,eAAC,CAAC,OAAO,EAAE,IAAA,WAAC,EAAA,GAAG,eAAC,CAAC,OAAO,eAAe,IAAI,MAAM,eAAC,CAAC,OAAO,WAAW,IAAI,GAAG,CAAC;aACpF,MAAM,CAAC,eAAC,CAAC,MAAM,EAAE,IAAA,WAAC,EAAA,GAAG,eAAC,CAAC,OAAO,SAAS,CAAC,CAAA;QAC3C,IAAA,qBAAY,EAAC,GAAG,CAAC,CAAA;IACnB,CAAC,EACD,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,CAClB,CAAA;AACH,CAAC;AAED,SAAS,iBAAiB,CAAC,EAAC,SAAS,EAAe,EAAE,GAA0B;IAC9E,IAAI,GAAG,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,MAAM;QAAE,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAA;AACrF,CAAC;AAED,SAAS,UAAU,CAAC,GAAY,EAAE,OAAe,EAAE,MAAiC;IAClF,IAAI,MAAM,KAAK,SAAS;QAAE,MAAM,IAAI,KAAK,CAAC,YAAY,OAAO,qBAAqB,CAAC,CAAA;IACnF,OAAO,GAAG,CAAC,UAAU,CACnB,SAAS,EACT,OAAO,MAAM,IAAI,UAAU,CAAC,CAAC,CAAC,EAAC,GAAG,EAAE,MAAM,EAAC,CAAC,CAAC,CAAC,EAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,IAAA,mBAAS,EAAC,MAAM,CAAC,EAAC,CACrF,CAAA;AACH,CAAC;AAED,SAAgB,eAAe,CAC7B,MAAe,EACf,UAAsB,EACtB,cAAc,GAAG,KAAK;IAEtB,iBAAiB;IACjB,OAAO,CACL,CAAC,UAAU,CAAC,MAAM;QAClB,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,CACrB,EAAE,KAAK,OAAO;YACZ,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;YACvB,CAAC,CAAC,EAAE,KAAK,QAAQ;gBACjB,CAAC,CAAC,MAAM,IAAI,OAAO,MAAM,IAAI,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;gBAC/D,CAAC,CAAC,OAAO,MAAM,IAAI,EAAE,IAAI,CAAC,cAAc,IAAI,OAAO,MAAM,IAAI,WAAW,CAAC,CAC5E,CACF,CAAA;AACH,CAAC;AAhBD,0CAgBC;AAED,SAAgB,oBAAoB,CAClC,EAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,aAAa,EAAe,EACjD,GAA2B,EAC3B,OAAe;IAEf,wBAAwB;IACxB,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,KAAK,OAAO,EAAE;QACzF,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;KAC5C;IAED,MAAM,IAAI,GAAG,GAAG,CAAC,YAAY,CAAA;IAC7B,IAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,EAAE;QAC3E,MAAM,IAAI,KAAK,CAAC,2CAA2C,OAAO,KAAK,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;KACzF;IAED,IAAI,GAAG,CAAC,cAAc,EAAE;QACtB,MAAM,KAAK,GAAG,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAA;QACjD,IAAI,CAAC,KAAK,EAAE;YACV,MAAM,GAAG,GACP,YAAY,OAAO,+BAA+B,aAAa,KAAK;gBACpE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;YAC5C,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK;gBAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;;gBACpD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SAC1B;KACF;AACH,CAAC;AAzBD,oDAyBC"}