{"name": "@vercel/redwood", "version": "2.3.3", "main": "./dist/index.js", "license": "Apache-2.0", "homepage": "https://vercel.com/docs", "files": ["dist"], "repository": {"type": "git", "url": "https://github.com/vercel/vercel.git", "directory": "packages/redwood"}, "dependencies": {"@vercel/nft": "0.29.2", "@vercel/static-config": "3.1.1", "semver": "6.3.1", "ts-morph": "12.0.0"}, "devDependencies": {"@types/aws-lambda": "8.10.19", "@types/node": "14.18.33", "@types/semver": "6.0.0", "@vercel/build-utils": "10.6.0", "@vercel/routing-utils": "5.0.5", "execa": "3.2.0", "fs-extra": "11.1.0", "jest-junit": "16.0.0"}, "scripts": {"build": "node ../../utils/build-builder.mjs", "type-check": "tsc --noEmit"}}