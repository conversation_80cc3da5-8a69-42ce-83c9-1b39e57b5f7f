{"version": 3, "file": "edge-vm.js", "sourceRoot": "", "sources": ["../src/edge-vm.ts"], "names": [], "mappings": ";;;AACA,wDAAsE;AAEtE,2BAA0C;AAC1C,6BAAyD;AAqBzD;;;GAGG;AACH,IAAI,0BAA8C,CAAA;AAClD,IAAI,yBAAyC,CAAA;AAE7C,MAAa,MAA4C,SAAQ,OAAK;IAGpE,YAAY,OAA0B;QACpC,KAAK,CAAC;YACJ,GAAG,OAAO;YACV,MAAM,EAAE,CAAC,OAAO,EAAE,EAAE;gBAClB,OAAO,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM;oBACpB,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;oBACxC,CAAC,CAAE,aAAa,CAAC,OAAO,CAAqB,CAAA;YACjD,CAAC;SACF,CAAC,CAAA;QAEF,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,gCAAgC,EAAE;YACpE,GAAG,EAAE,kCAAkC;YACvC,YAAY,EAAE,KAAK;YACnB,UAAU,EAAE,KAAK;SAClB,CAAC,CAAA;QACF,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,qBAAqB,EAAE;YACjD,GAAG,EAAE,GAAG,EAAE,CAAC,0BAA0B;YACrC,YAAY,EAAE,KAAK;YACnB,UAAU,EAAE,KAAK;SAClB,CAAC,CAAA;QAEF,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,mBAAmB,EAAE;YACvD,GAAG,EAAE,iCAAiC;YACtC,YAAY,EAAE,KAAK;YACnB,UAAU,EAAE,KAAK;SAClB,CAAC,CAAA;QACF,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,iBAAiB,EAAE;YAC7C,GAAG,EAAE,GAAG,EAAE,CAAC,yBAAyB;YACpC,YAAY,EAAE,KAAK;YACnB,UAAU,EAAE,KAAK;SAClB,CAAC,CAAA;QAEF,IAAI,CAAC,QAAQ,CAAO,2BAA2B,EAAE,CAAC,CAAA;QAClD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAgB,oBAAoB,EAAE,CAAC,CAAA;QACzE,KAAK,MAAM,IAAI,IAAI,wBAAwB,EAAE,CAAC;YAC5C,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;QACrC,CAAC;QAED,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,EAAE,CAAC;YACzB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,CAAA;QACpC,CAAC;IACH,CAAC;CACF;AA7CD,wBA6CC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;AACH,MAAM,wBAAwB,GAAG;IAC/B,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,YAAY;IACZ,aAAa;IACb,OAAO;IACP,aAAa;IACb,WAAW;CACH,CAAA;AAEV,SAAS,eAAe,CAAC,IAAY,EAAE,GAAQ;IAC7C,aAAa;IACb,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAA;IAE5C,OAAO,IAAA,iBAAY,EACjB;mBACe,IAAI,gBAAgB,IAAI;;uEAE4B,IAAI;6DACd,IAAI;;;;;;;;;;;;;KAa5D,EACD,GAAG,CACJ,CAAA;AACH,CAAC;AAED;;;;GAIG;AACH,SAAS,kCAAkC,CAAC,QAA4B;IACtE,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAChC,OAAO,CAAC,EAAE,CACR,oBAAoB,EACpB,SAAS,uBAAuB,CAAC,MAAM,EAAE,OAAO;YAC9C,0BAA0B,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAC7C,OAAO,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAC7B,CAAA;QACH,CAAC,CACF,CAAA;IACH,CAAC;IACD,0BAA0B,GAAG,QAAQ,CAAA;AACvC,CAAC;AAED,SAAS,iCAAiC,CAAC,QAAwB;IACjE,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAC/B,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,SAAS,mBAAmB,CAAC,KAAK;YAChE,yBAAyB,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAA;QAChE,CAAC,CAAC,CAAA;IACJ,CAAC;IACD,yBAAyB,GAAG,QAAQ,CAAA;AACtC,CAAC;AAED;;;;;;GAMG;AACH,SAAS,2BAA2B;IAClC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAwCN,CAAA;AACH,CAAC;AAED;;;;GAIG;AACH,SAAS,oBAAoB;IAC3B,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAoCJ,CAAA;AACL,CAAC;AA6CD,SAAS,aAAa,CAAC,OAAkB;IACvC,cAAc,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAA;IACrE,cAAc,CAAC,OAAO,EAAE,YAAY,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAA;IACzD,cAAc,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAA;IACpD,cAAc,CAAC,OAAO,EAAE,eAAe,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,CAAA;IAClE,cAAc,CAAC,OAAO,EAAE,cAAc,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC,CAAA;IAChE,cAAc,CAAC,OAAO,EAAE,gBAAgB,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC,CAAA;IACpE,cAAc,CAAC,OAAO,EAAE,aAAa,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC,CAAA;IAEjE,MAAM,aAAa,GAAG,oCAAoC,CAAC,OAAO,CAAC,CAAA;IAEnE,gBAAgB,CAAC,OAAO,EAAE;QACxB,OAAO,EAAE,IAAA,WAAc,EAAC;YACtB,GAAG,aAAa;YAChB,OAAO,EAAE,IAAA,iBAAY,EAAC,SAAS,EAAE,OAAO,CAAC;SAC1C,CAAC;QACF,UAAU,EAAE,CAAC,QAAQ,CAAC;QACtB,aAAa,EAAE;YACb,SAAS;YACT,QAAQ;YACR,WAAW;YACX,cAAc;YAEd,aAAa;YACb,OAAO;YACP,MAAM;YACN,UAAU;YACV,SAAS;YACT,SAAS;YACT,UAAU;YACV,WAAW;YAEX,mBAAmB;YACnB,iBAAiB;YAEjB,OAAO;YACP,MAAM;YAEN,MAAM;YACN,KAAK;YACL,iBAAiB;YACjB,YAAY;YAEZ,kBAAkB;YAClB,iBAAiB;YACjB,aAAa;YACb,cAAc;YAEd,UAAU;YACV,gBAAgB;YAChB,0BAA0B;YAC1B,6BAA6B;YAC7B,mBAAmB;YACnB,mBAAmB;YACnB,iBAAiB;YACjB,gBAAgB;YAChB,6BAA6B;YAE7B,WAAW;YACX,MAAM;YACN,MAAM;YACN,aAAa;YACb,aAAa;YAEb,SAAS;YACT,OAAO;YACP,aAAa;YACb,YAAY;YACZ,uBAAuB;YAEvB,UAAU;YACV,SAAS;YAET,cAAc;YACd,aAAa;YAEb,SAAS;YACT,YAAY;YACZ,aAAa;SACd;KACF,CAAC,CAAA;IAEF,OAAO,OAAsB,CAAA;AAC/B,CAAC;AAED,SAAS,cAAc,CAAC,GAAQ,EAAE,IAAY,EAAE,KAAyB;;IACvE,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,EAAE;QAC/B,YAAY,EAAE,MAAA,KAAK,CAAC,YAAY,mCAAI,KAAK;QACzC,UAAU,EAAE,MAAA,KAAK,CAAC,UAAU,mCAAI,KAAK;QACrC,KAAK,EAAE,KAAK,CAAC,KAAK;QAClB,QAAQ,EAAE,MAAA,KAAK,CAAC,QAAQ,mCAAI,IAAI;KACjC,CAAC,CAAA;AACJ,CAAC;AAED,SAAS,gBAAgB,CACvB,OAAY,EACZ,OAIC;;IAED,KAAK,MAAM,QAAQ,IAAI,MAAA,OAAO,CAAC,UAAU,mCAAI,EAAE,EAAE,CAAC;QAChD,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,2CAA2C,QAAQ,GAAG,CAAC,CAAA;QACzE,CAAC;QAED,cAAc,CAAC,OAAO,EAAE,QAAQ,EAAE;YAChC,UAAU,EAAE,IAAI;YAChB,KAAK,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC;SACjC,CAAC,CAAA;IACJ,CAAC;IAED,KAAK,MAAM,QAAQ,IAAI,MAAA,OAAO,CAAC,aAAa,mCAAI,EAAE,EAAE,CAAC;QACnD,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,2CAA2C,QAAQ,GAAG,CAAC,CAAA;QACzE,CAAC;QAED,cAAc,CAAC,OAAO,EAAE,QAAQ,EAAE;YAChC,KAAK,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC;SACjC,CAAC,CAAA;IACJ,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,SAAS,oCAAoC,CAC3C,OAAgB;IAEhB,MAAM,IAAI,GAAG,wBAAwB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC/C,MAAM,gBAAgB,GAAG,KAAK,IAAI,IAAI,CAAA;IACtC,OAAO,IAAA,iBAAY,EAAC,gBAAgB,EAAE,OAAO,CAAC,CAAA;AAChD,CAAC", "sourcesContent": ["import type * as EdgePrimitives from '@edge-runtime/primitives'\nimport { load as loadPrimitives } from '@edge-runtime/primitives/load'\nimport type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>r<PERSON><PERSON><PERSON>, Rejection<PERSON>and<PERSON> } from './types'\nimport { Context, runInContext } from 'vm'\nimport { VM, type VMContext, type VMOptions } from './vm'\n\nexport interface EdgeVMOptions<T extends EdgeContext> {\n  /**\n   * Provide code generation options to the Node.js VM.\n   * If you don't provide any option, code generation will be disabled.\n   */\n  codeGeneration?: VMOptions<T>['codeGeneration']\n  /**\n   * Allows to extend the VMContext. Note that it must return a contextified\n   * object so ideally it should return the same reference it receives.\n   */\n  extend?: (context: EdgeContext) => EdgeContext & T\n  /**\n   * Code to be evaluated as when the Edge Runtime is created. This is handy\n   * to run code directly instead of first creating the runtime and then\n   * evaluating.\n   */\n  initialCode?: string\n}\n\n/**\n * Store handlers that the user defined from code so that we can invoke them\n * from the Node.js realm.\n */\nlet unhandledRejectionHandlers: RejectionHandler[]\nlet uncaughtExceptionHandlers: ErrorHandler[]\n\nexport class EdgeVM<T extends EdgeContext = EdgeContext> extends VM<T> {\n  public readonly dispatchFetch: DispatchFetch\n\n  constructor(options?: EdgeVMOptions<T>) {\n    super({\n      ...options,\n      extend: (context) => {\n        return options?.extend\n          ? options.extend(addPrimitives(context))\n          : (addPrimitives(context) as EdgeContext & T)\n      },\n    })\n\n    Object.defineProperty(this.context, '__onUnhandledRejectionHandlers', {\n      set: registerUnhandledRejectionHandlers,\n      configurable: false,\n      enumerable: false,\n    })\n    Object.defineProperty(this, '__rejectionHandlers', {\n      get: () => unhandledRejectionHandlers,\n      configurable: false,\n      enumerable: false,\n    })\n\n    Object.defineProperty(this.context, '__onErrorHandlers', {\n      set: registerUncaughtExceptionHandlers,\n      configurable: false,\n      enumerable: false,\n    })\n    Object.defineProperty(this, '__errorHandlers', {\n      get: () => uncaughtExceptionHandlers,\n      configurable: false,\n      enumerable: false,\n    })\n\n    this.evaluate<void>(getDefineEventListenersCode())\n    this.dispatchFetch = this.evaluate<DispatchFetch>(getDispatchFetchCode())\n    for (const name of transferableConstructors) {\n      patchInstanceOf(name, this.context)\n    }\n\n    if (options?.initialCode) {\n      this.evaluate(options.initialCode)\n    }\n  }\n}\n\n/**\n * Transferable constructors are the constructors that we expect to be\n * \"shared\" between the realms.\n *\n * When a user creates an instance of one of these constructors, we want\n * to make sure that the `instanceof` operator works as expected:\n *\n * * If the instance was created in the Node.js realm, then `instanceof`\n *   should return true when used in the EdgeVM realm.\n * * If the instance was created in the EdgeVM realm, then `instanceof`\n *   should return true when used in the EdgeVM realm.\n *\n * For example, the return value from `new TextEncoder().encode(\"hello\")` is a\n * Uint8Array. Since `TextEncoder` implementation is coming from the Node.js realm,\n * therefore the following will be false, which doesn't fit the expectation of the user:\n * ```ts\n * new TextEncoder().encode(\"hello\") instanceof Uint8Array\n * ```\n *\n * This is because the `Uint8Array` in the `vm` context is not the same\n * as the one in the Node.js realm.\n *\n * Patching the constructors in the `vm` is done by the {@link patchInstanceOf}\n * function, and this is the list of constructors that need to be patched.\n *\n * These constructors are also being injected as \"globals\" when the VM is\n * constructed, by passing them as arguments to the {@link loadPrimitives}\n * function.\n */\nconst transferableConstructors = [\n  'Object',\n  'Array',\n  'RegExp',\n  'Uint8Array',\n  'ArrayBuffer',\n  'Error',\n  'SyntaxError',\n  'TypeError',\n] as const\n\nfunction patchInstanceOf(item: string, ctx: any) {\n  // @ts-ignore\n  ctx[Symbol.for(`node:${item}`)] = eval(item)\n\n  return runInContext(\n    `\n      globalThis.${item} = new Proxy(${item}, {\n        get(target, prop, receiver) {\n          if (prop === Symbol.hasInstance && receiver === globalThis.${item}) {\n            const nodeTarget = globalThis[Symbol.for('node:${item}')];\n            if (nodeTarget) {\n              return function(instance) {\n                return instance instanceof target || instance instanceof nodeTarget;\n              };\n            } else {\n              throw new Error('node target must exist')\n            }\n          }\n\n          return Reflect.get(target, prop, receiver);\n        }\n      })\n    `,\n    ctx,\n  )\n}\n\n/**\n * Register system-level handlers to make sure that we report to the user\n * whenever there is an unhandled rejection or exception before the process crashes.\n * Do it on demand so we don't swallow rejections/errors for no reason.\n */\nfunction registerUnhandledRejectionHandlers(handlers: RejectionHandler[]) {\n  if (!unhandledRejectionHandlers) {\n    process.on(\n      'unhandledRejection',\n      function invokeRejectionHandlers(reason, promise) {\n        unhandledRejectionHandlers.forEach((handler) =>\n          handler({ reason, promise }),\n        )\n      },\n    )\n  }\n  unhandledRejectionHandlers = handlers\n}\n\nfunction registerUncaughtExceptionHandlers(handlers: ErrorHandler[]) {\n  if (!uncaughtExceptionHandlers) {\n    process.on('uncaughtException', function invokeErrorHandlers(error) {\n      uncaughtExceptionHandlers.forEach((handler) => handler(error))\n    })\n  }\n  uncaughtExceptionHandlers = handlers\n}\n\n/**\n * Generates polyfills for addEventListener and removeEventListener. It keeps\n * all listeners in hidden property __listeners. It will also call a hook\n * `__onUnhandledRejectionHandler` and `__onErrorHandler` when unhandled rejection\n * events are added or removed and prevent from having more than one FetchEvent\n * handler.\n */\nfunction getDefineEventListenersCode() {\n  return `\n    Object.defineProperty(self, '__listeners', {\n      configurable: false,\n      enumerable: false,\n      value: {},\n      writable: true,\n    })\n\n    function __conditionallyUpdatesHandlerList(eventType) {\n      if (eventType === 'unhandledrejection') {\n        self.__onUnhandledRejectionHandlers = self.__listeners[eventType];\n      } else if (eventType === 'error') {\n        self.__onErrorHandlers = self.__listeners[eventType];\n      }\n    }\n\n    function addEventListener(type, handler) {\n      const eventType = type.toLowerCase();\n      if (eventType === 'fetch' && self.__listeners.fetch) {\n        throw new TypeError('You can register just one \"fetch\" event listener');\n      }\n\n      self.__listeners[eventType] = self.__listeners[eventType] || [];\n      self.__listeners[eventType].push(handler);\n      __conditionallyUpdatesHandlerList(eventType);\n    }\n\n    function removeEventListener(type, handler) {\n      const eventType = type.toLowerCase();\n      if (self.__listeners[eventType]) {\n        self.__listeners[eventType] = self.__listeners[eventType].filter(item => {\n          return item !== handler;\n        });\n\n        if (self.__listeners[eventType].length === 0) {\n          delete self.__listeners[eventType];\n        }\n      }\n      __conditionallyUpdatesHandlerList(eventType);\n    }\n  `\n}\n\n/**\n * Generates the code to dispatch a FetchEvent invoking the handlers defined\n * for such events. In case there is no event handler defined it will throw\n * an error.\n */\nfunction getDispatchFetchCode() {\n  return `(async function dispatchFetch(input, init) {\n    const request = new Request(input, init);\n    const event = new FetchEvent(request);\n    if (!self.__listeners.fetch) {\n      throw new Error(\"No fetch event listeners found\");\n    }\n\n    const getResponse = ({ response, error }) => {\n     if (error || !response || !(response instanceof Response)) {\n        console.error(error ? error.toString() : 'The event listener did not respond')\n        response = new Response(null, {\n          statusText: 'Internal Server Error',\n          status: 500\n        })\n      }\n\n      response.waitUntil = () => Promise.all(event.awaiting);\n\n      if (response.status < 300 || response.status >= 400 ) {\n        response.headers.delete('content-encoding');\n        response.headers.delete('transform-encoding');\n        response.headers.delete('content-length');\n      }\n\n      return response;\n    }\n\n    try {\n      await self.__listeners.fetch[0].call(event, event)\n    } catch (error) {\n      return getResponse({ error })\n    }\n\n    return Promise.resolve(event.response)\n      .then(response => getResponse({ response }))\n      .catch(error => getResponse({ error }))\n  })`\n}\n\nexport type EdgeContext = VMContext & {\n  self: EdgeContext\n  globalThis: EdgeContext\n  AbortController: typeof EdgePrimitives.AbortController\n  AbortSignal: typeof EdgePrimitives.AbortSignal\n  atob: typeof EdgePrimitives.atob\n  Blob: typeof EdgePrimitives.Blob\n  btoa: typeof EdgePrimitives.btoa\n  console: typeof EdgePrimitives.console\n  crypto: typeof EdgePrimitives.crypto\n  Crypto: typeof EdgePrimitives.Crypto\n  CryptoKey: typeof EdgePrimitives.CryptoKey\n  DOMException: typeof EdgePrimitives.DOMException\n  Event: typeof EdgePrimitives.Event\n  EventTarget: typeof EdgePrimitives.EventTarget\n  fetch: typeof EdgePrimitives.fetch\n  FetchEvent: typeof EdgePrimitives.FetchEvent\n  File: typeof EdgePrimitives.File\n  FormData: typeof EdgePrimitives.FormData\n  Headers: typeof EdgePrimitives.Headers\n  PromiseRejectionEvent: typeof EdgePrimitives.PromiseRejectionEvent\n  ReadableStream: typeof EdgePrimitives.ReadableStream\n  ReadableStreamBYOBReader: typeof EdgePrimitives.ReadableStreamBYOBReader\n  ReadableStreamDefaultReader: typeof EdgePrimitives.ReadableStreamDefaultReader\n  Request: typeof EdgePrimitives.Request\n  Response: typeof EdgePrimitives.Response\n  setTimeout: typeof EdgePrimitives.setTimeout\n  setInterval: typeof EdgePrimitives.setInterval\n  structuredClone: typeof EdgePrimitives.structuredClone\n  SubtleCrypto: typeof EdgePrimitives.SubtleCrypto\n  TextDecoder: typeof EdgePrimitives.TextDecoder\n  TextDecoderStream: typeof EdgePrimitives.TextDecoderStream\n  TextEncoder: typeof EdgePrimitives.TextEncoder\n  TextEncoderStream: typeof EdgePrimitives.TextEncoderStream\n  TransformStream: typeof EdgePrimitives.TransformStream\n  URL: typeof EdgePrimitives.URL\n  URLPattern: typeof EdgePrimitives.URLPattern\n  URLSearchParams: typeof EdgePrimitives.URLSearchParams\n  WritableStream: typeof EdgePrimitives.WritableStream\n  WritableStreamDefaultWriter: typeof EdgePrimitives.WritableStreamDefaultWriter\n  EdgeRuntime: string\n}\n\nfunction addPrimitives(context: VMContext) {\n  defineProperty(context, 'self', { enumerable: true, value: context })\n  defineProperty(context, 'globalThis', { value: context })\n  defineProperty(context, 'Symbol', { value: Symbol })\n  defineProperty(context, 'clearInterval', { value: clearInterval })\n  defineProperty(context, 'clearTimeout', { value: clearTimeout })\n  defineProperty(context, 'queueMicrotask', { value: queueMicrotask })\n  defineProperty(context, 'EdgeRuntime', { value: 'edge-runtime' })\n\n  const transferables = getTransferablePrimitivesFromContext(context)\n\n  defineProperties(context, {\n    exports: loadPrimitives({\n      ...transferables,\n      WeakRef: runInContext(`WeakRef`, context),\n    }),\n    enumerable: ['crypto'],\n    nonenumerable: [\n      // Crypto\n      'Crypto',\n      'CryptoKey',\n      'SubtleCrypto',\n\n      // Fetch APIs\n      'fetch',\n      'File',\n      'FormData',\n      'Headers',\n      'Request',\n      'Response',\n      'WebSocket',\n\n      // Structured Clone\n      'structuredClone',\n\n      // Blob\n      'Blob',\n\n      // URL\n      'URL',\n      'URLSearchParams',\n      'URLPattern',\n\n      // AbortController\n      'AbortController',\n      'AbortSignal',\n      'DOMException',\n\n      // Streams\n      'ReadableStream',\n      'ReadableStreamBYOBReader',\n      'ReadableStreamDefaultReader',\n      'TextDecoderStream',\n      'TextEncoderStream',\n      'TransformStream',\n      'WritableStream',\n      'WritableStreamDefaultWriter',\n\n      // Encoding\n      'atob',\n      'btoa',\n      'TextEncoder',\n      'TextDecoder',\n\n      // Events\n      'Event',\n      'EventTarget',\n      'FetchEvent',\n      'PromiseRejectionEvent',\n\n      // Console\n      'console',\n\n      // Performance\n      'performance',\n\n      // Timers\n      'setTimeout',\n      'setInterval',\n    ],\n  })\n\n  return context as EdgeContext\n}\n\nfunction defineProperty(obj: any, prop: string, attrs: PropertyDescriptor) {\n  Object.defineProperty(obj, prop, {\n    configurable: attrs.configurable ?? false,\n    enumerable: attrs.enumerable ?? false,\n    value: attrs.value,\n    writable: attrs.writable ?? true,\n  })\n}\n\nfunction defineProperties(\n  context: any,\n  options: {\n    exports: Record<string, any>\n    enumerable?: string[]\n    nonenumerable?: string[]\n  },\n) {\n  for (const property of options.enumerable ?? []) {\n    if (!options.exports[property]) {\n      throw new Error(`Attempt to export a nullable value for \"${property}\"`)\n    }\n\n    defineProperty(context, property, {\n      enumerable: true,\n      value: options.exports[property],\n    })\n  }\n\n  for (const property of options.nonenumerable ?? []) {\n    if (!options.exports[property]) {\n      throw new Error(`Attempt to export a nullable value for \"${property}\"`)\n    }\n\n    defineProperty(context, property, {\n      value: options.exports[property],\n    })\n  }\n}\n\n/**\n * Create an object that contains all the {@link transferableConstructors}\n * implemented in the provided context.\n */\nfunction getTransferablePrimitivesFromContext(\n  context: Context,\n): Record<(typeof transferableConstructors)[number], unknown> {\n  const keys = transferableConstructors.join(',')\n  const stringifedObject = `({${keys}})`\n  return runInContext(stringifedObject, context)\n}\n"]}