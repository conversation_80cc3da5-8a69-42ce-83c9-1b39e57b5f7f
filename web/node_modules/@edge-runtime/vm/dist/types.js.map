{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../src/types.ts"], "names": [], "mappings": "", "sourcesContent": ["export interface DispatchFetch {\n  (\n    input: string,\n    init?: RequestInit,\n  ): Promise<\n    Response & {\n      waitUntil: () => Promise<any>\n    }\n  >\n}\n\nexport interface RejectionHandler {\n  (reason?: {} | null, promise?: Promise<any>): void\n}\n\nexport interface ErrorHandler {\n  (error?: {} | null): void\n}\n"]}