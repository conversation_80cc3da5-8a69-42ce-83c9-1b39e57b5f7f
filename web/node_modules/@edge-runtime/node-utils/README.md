<div align="center">
  <br>
  <img src="https://user-images.githubusercontent.com/2096101/235130063-e561514e-1f66-4ff6-9034-70dbf7ca3260.png#gh-dark-mode-only">
  <img src="https://user-images.githubusercontent.com/2096101/235127419-ac6fe609-d0cd-4339-a593-c48305a83823.png#gh-light-mode-only">
  <br>
  <br>
  <p align="center"><strong>@edge-runtime/node-utils</strong>: A set of helpers for running edge-compliant code in Node.js environment.</p>
  <p align="center">See <a href="https://edge-runtime.vercel.app/packages/node-utils" target='_blank' rel='noopener noreferrer'>@edge-runtime/node-utils</a> section in our <a href="https://edge-runtime.vercel.app/" target='_blank' rel='noopener noreferrer'>website</a> for more information.</p>
  <br>
</div>

> **Note**: This is an alpha version.

## Install

Using npm:

```sh
npm install @edge-runtime/node-utils --save
```

or using yarn:

```sh
yarn add @edge-runtime/node-utils --dev
```

or using pnpm:

```sh
pnpm install @edge-runtime/node-utils --save
```

## License

**@edge-runtime/node-utils** © [Vercel](https://vercel.com), released under the [MPLv2](https://github.com/vercel/edge-runtime/blob/main/LICENSE.md) License.<br>
Authored and maintained by [Vercel](https://vercel.com) with help from [contributors](https://github.com/vercel/edge-runtime/contributors).

> [vercel.com](https://vercel.com) · GitHub [Vercel](https://github.com/vercel) · Twitter [@vercel](https://twitter.com/vercel)
