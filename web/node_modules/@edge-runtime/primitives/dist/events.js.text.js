module.exports = "\"use strict\";\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __name = (target, value) => __defProp(target, \"name\", { value, configurable: true });\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/primitives/events.js\nvar events_exports = {};\n__export(events_exports, {\n  FetchEvent: () => FetchEvent,\n  PromiseRejectionEvent: () => PromiseRejectionEvent\n});\nmodule.exports = __toCommonJS(events_exports);\nvar _FetchEvent = class _FetchEvent extends Event {\n  constructor(request) {\n    super(\"fetch\");\n    this.request = request;\n    this.response = null;\n    this.awaiting = /* @__PURE__ */ new Set();\n  }\n  respondWith = (response) => {\n    this.response = response;\n  };\n  waitUntil = (promise) => {\n    this.awaiting.add(promise);\n    promise.finally(() => this.awaiting.delete(promise));\n  };\n};\n__name(_FetchEvent, \"FetchEvent\");\nvar FetchEvent = _FetchEvent;\nvar _PromiseRejectionEvent = class _PromiseRejectionEvent extends Event {\n  constructor(type, init) {\n    super(type, { cancelable: true });\n    this.promise = init.promise;\n    this.reason = init.reason;\n  }\n};\n__name(_PromiseRejectionEvent, \"PromiseRejectionEvent\");\nvar PromiseRejectionEvent = _PromiseRejectionEvent;\n// Annotate the CommonJS export names for ESM import in node:\n0 && (module.exports = {\n  FetchEvent,\n  PromiseRejectionEvent\n});\n"