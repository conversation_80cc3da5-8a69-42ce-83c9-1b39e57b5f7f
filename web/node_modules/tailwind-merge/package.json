{"name": "tailwind-merge", "version": "2.6.0", "description": "Merge Tailwind CSS classes without style conflicts", "keywords": ["tailwindcss", "tailwind", "css", "classes", "className", "classList", "merge", "conflict", "override"], "homepage": "https://github.com/dcastil/tailwind-merge", "bugs": {"url": "https://github.com/dcastil/tailwind-merge/issues"}, "funding": {"type": "github", "url": "https://github.com/sponsors/dcastil"}, "license": "MIT", "author": "<PERSON><PERSON>", "files": ["dist", "src"], "source": "src/index.ts", "exports": {".": {"types": "./dist/types.d.ts", "require": "./dist/bundle-cjs.js", "import": "./dist/bundle-mjs.mjs", "default": "./dist/bundle-mjs.mjs"}, "./es5": {"types": "./dist/types.d.ts", "require": "./dist/es5/bundle-cjs.js", "import": "./dist/es5/bundle-mjs.mjs", "default": "./dist/es5/bundle-mjs.mjs"}}, "main": "./dist/bundle-cjs.js", "types": "./dist/types.d.ts", "repository": {"type": "git", "url": "https://github.com/dcastil/tailwind-merge.git"}, "sideEffects": false, "scripts": {"build": "rollup --config scripts/rollup.config.mjs", "bench": "vitest bench --config scripts/vitest.config.mts", "test": "vitest --config scripts/vitest.config.mts --coverage", "test:watch": "vitest --config scripts/vitest.config.mts", "test:exports": "node scripts/test-built-package-exports.cjs && node scripts/test-built-package-exports.mjs", "lint": "eslint --max-warnings 0 '**'", "preversion": "if [ -n \"$DANYS_MACHINE\" ]; then git checkout main && git pull; fi", "version": "zx scripts/update-readme.mjs", "postversion": "if [ -n \"$DANYS_MACHINE\" ]; then git push --follow-tags && open https://github.com/dcastil/tailwind-merge/releases; fi"}, "dependencies": {}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/preset-env": "^7.26.0", "@codspeed/vitest-plugin": "^4.0.0", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-typescript": "^12.1.1", "@vitest/coverage-v8": "^2.1.8", "@vitest/eslint-plugin": "^1.1.14", "babel-plugin-annotate-pure-calls": "^0.4.0", "babel-plugin-polyfill-regenerator": "^0.6.3", "eslint": "^9.16.0", "eslint-plugin-import": "^2.31.0", "globby": "^11.1.0", "prettier": "^3.4.2", "rollup": "^4.28.1", "rollup-plugin-delete": "^2.1.0", "rollup-plugin-dts": "^6.1.1", "tslib": "^2.8.1", "typescript": "^5.7.2", "typescript-eslint": "^8.17.0", "vitest": "^2.1.8", "zx": "^8.2.4"}, "publishConfig": {"provenance": true}}