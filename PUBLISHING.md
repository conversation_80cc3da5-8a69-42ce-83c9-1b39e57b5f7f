# Publishing Guide for Microservice CLI

This guide walks you through publishing the Microservice CLI to npm.

## Prerequisites

1. **npm Account**: Create an account at [npmjs.com](https://www.npmjs.com/)
2. **npm CLI**: Ensure you have npm installed and are logged in
3. **Git Repository**: Set up a GitHub repository for the project

## Pre-Publishing Checklist

### 1. Update Package Information

Before publishing, update these fields in `package.json`:

```json
{
  "name": "@your-org/microservice-cli",  // Change to your org/username
  "author": "Your Name <<EMAIL>>",
  "repository": {
    "type": "git",
    "url": "https://github.com/your-org/microservice-cli.git"
  },
  "bugs": {
    "url": "https://github.com/your-org/microservice-cli/issues"
  },
  "homepage": "https://github.com/your-org/microservice-cli#readme"
}
```

### 2. Test the CLI Locally

```bash
# Build the project
npm run build

# Test the CLI locally
npm link
microservice-cli create test-project --dry-run
npm unlink
```

### 3. Run Quality Checks

```bash
# Run tests
npm test

# Run linting
npm run lint

# Check what will be published
npm pack --dry-run
```

## Publishing Steps

### Step 1: Login to npm

```bash
npm login
```

Enter your npm credentials when prompted.

### Step 2: Choose Publishing Method

#### Option A: Manual Publishing

```bash
# For first release
npm publish

# For patch updates (1.0.0 -> 1.0.1)
npm run publish:patch

# For minor updates (1.0.0 -> 1.1.0)
npm run publish:minor

# For major updates (1.0.0 -> 2.0.0)
npm run publish:major
```

#### Option B: Scoped Package (Recommended)

If using a scoped package name like `@your-org/microservice-cli`:

```bash
npm publish --access public
```

### Step 3: Verify Publication

1. Check your package on npm: `https://www.npmjs.com/package/@your-org/microservice-cli`
2. Test installation: `npm install -g @your-org/microservice-cli`
3. Test the CLI: `microservice-cli --help`

## Version Management

### Semantic Versioning

- **Patch** (1.0.0 -> 1.0.1): Bug fixes
- **Minor** (1.0.0 -> 1.1.0): New features (backward compatible)
- **Major** (1.0.0 -> 2.0.0): Breaking changes

### Automated Versioning

The package includes scripts for automated versioning:

```bash
npm run publish:patch   # Increments patch version and publishes
npm run publish:minor   # Increments minor version and publishes
npm run publish:major   # Increments major version and publishes
```

## Continuous Integration (Optional)

### GitHub Actions for Auto-Publishing

Create `.github/workflows/publish.yml`:

```yaml
name: Publish to npm

on:
  release:
    types: [published]

jobs:
  publish:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          registry-url: 'https://registry.npmjs.org'
      - run: npm ci
      - run: npm test
      - run: npm run build
      - run: npm publish --access public
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
```

## Post-Publishing

### 1. Update Documentation

- Update README.md with installation instructions
- Create GitHub releases with changelog
- Update any documentation sites

### 2. Promote Your CLI

- Share on social media
- Submit to awesome lists
- Write blog posts or tutorials

### 3. Monitor and Maintain

- Monitor npm download stats
- Respond to issues and PRs
- Keep dependencies updated

## Troubleshooting

### Common Issues

1. **Package name already exists**: Choose a different name or use a scoped package
2. **Permission denied**: Ensure you're logged in and have publish rights
3. **Build failures**: Run `npm run build` before publishing
4. **Test failures**: Fix failing tests before publishing

### Useful Commands

```bash
# Check npm login status
npm whoami

# View package info
npm view @your-org/microservice-cli

# Unpublish (within 24 hours)
npm unpublish @your-org/microservice-cli@1.0.0

# Deprecate a version
npm deprecate @your-org/microservice-cli@1.0.0 "Please upgrade to 1.0.1"
```

## Security Considerations

1. **Never commit npm tokens** to version control
2. **Use 2FA** on your npm account
3. **Regularly audit dependencies**: `npm audit`
4. **Keep dependencies updated**: `npm update`

## Support

If you encounter issues during publishing:

1. Check npm status: https://status.npmjs.org/
2. Review npm documentation: https://docs.npmjs.com/
3. Ask for help in npm community forums
