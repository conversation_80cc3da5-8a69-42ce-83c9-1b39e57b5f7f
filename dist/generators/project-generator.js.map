{"version": 3, "file": "project-generator.js", "sourceRoot": "", "sources": ["../../src/generators/project-generator.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAwB;AAGxB,6DAAsD;AACtD,mEAA4D;AAO5D,iEAA0D;AAE1D;;GAEG;AACH,MAAa,gBAAgB;IAO3B,YAAY,MAAc,EAAE,SAAoB;QAC9C,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,IAAI,mCAAc,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAC5D,IAAI,CAAC,gBAAgB,GAAG,IAAI,uCAAgB,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QACrF,IAAI,CAAC,iBAAiB,GAAG,IAAI,yCAAiB,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;IACzF,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CACnB,aAA4B,EAC5B,QAAyB,EACzB,QAAoC,EACpC,OAAyB;QAEzB,MAAM,WAAW,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC;QAErE,yBAAyB;QACzB,MAAM,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;QAEtD,2BAA2B;QAC3B,MAAM,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;QAEvE,oBAAoB;QACpB,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,QAAQ,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;QAC7E,CAAC;QAED,oBAAoB;QACpB,IAAI,QAAQ,EAAE,CAAC;YACb,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,QAAQ,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;QAC7E,CAAC;QAED,0CAA0C;QAC1C,MAAM,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,aAAa,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;QAE1F,8BAA8B;QAC9B,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,aAAa,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;IACxF,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB,CAAC,WAAmB,EAAE,OAAyB;QAC/E,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QAEnE,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YAC/B,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;YAChE,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAClD,CAAC;QACH,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAClC,WAAmB,EACnB,cAA6B,EAC7B,OAAyB;QAEzB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,+BAA+B,CAAC,CAAC;QAEtD,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACpB,MAAM,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAClD,MAAM,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC,CAAC;YACzE,MAAM,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC;YACrE,MAAM,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAC5B,WAAmB,EACnB,QAAyB,EACzB,aAA4B,EAC5B,OAAyB;QAEzB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,wBAAwB,CAAC,CAAC;QAE/C,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,MAAM,WAAW,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;YAErE,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;gBACpB,MAAM,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YACpD,CAAC;YAED,MAAM,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,WAAW,EAAE,OAAO,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;QAC5F,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,aAAa,QAAQ,CAAC,MAAM,aAAa,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAC5B,WAAmB,EACnB,QAAwB,EACxB,aAA4B,EAC5B,OAAyB;QAEzB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,wBAAwB,CAAC,CAAC;QAE/C,MAAM,YAAY,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;QAE3D,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACpB,MAAM,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;QACrD,CAAC;QAED,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,YAAY,EAAE,QAAQ,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;QAE9F,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CACjC,WAAmB,EACnB,cAA6B,EAC7B,QAAyB,EACzB,QAAoC,EACpC,OAAyB;QAEzB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,mCAAmC,CAAC,CAAC;QAE1D,8BAA8B;QAC9B,MAAM,oBAAoB,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAE5E,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACpB,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAC5B,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,oBAAoB,CAAC,EAC5C,oBAAoB,CACrB,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAC7B,WAAmB,EACnB,aAA4B,EAC5B,QAAyB,EACzB,QAAoC,EACpC,OAAyB;QAEzB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,kCAAkC,CAAC,CAAC;QAEzD,qBAAqB;QACrB,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAE7E,yCAAyC;QACzC,MAAM,kBAAkB,GAAG,IAAI,CAAC,uBAAuB,CAAC,aAAa,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAE3F,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACpB,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE,aAAa,CAAC,CAAC;YACnF,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,EAAE,kBAAkB,CAAC,CAAC;QAC7F,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,WAAmB;QAChD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,WAAW,eAAe,CAAC,CAAC;QAE1D,MAAM,QAAQ,GAAG,wDAAa,UAAU,GAAC,CAAC;QAC1C,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC;YAChD;gBACE,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE,uDAAuD;gBAChE,OAAO,EAAE,KAAK;aACf;SACF,CAAC,CAAC;QAEH,OAAO,OAAkB,CAAC;IAC5B,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,QAAyB,EAAE,QAAoC;QAC3F,MAAM,kBAAkB,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;IACnD,OAAO,CAAC,IAAI;wBACQ,OAAO,CAAC,IAAI;;WAEzB,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI;;;eAGxB,OAAO,CAAC,IAAI;;qBAEN,OAAO,CAAC,IAAI;0BACP,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEnC,MAAM,kBAAkB,GAAG,QAAQ,CAAC,CAAC,CAAC;IACtC,QAAQ,CAAC,IAAI;eACF,QAAQ,CAAC,IAAI;;;;;;YAMhB,QAAQ,CAAC,IAAI;0BACC,CAAC,CAAC,CAAC,EAAE,CAAC;QAE5B,OAAO;;WAEA,kBAAkB,GAAG,kBAAkB;;;;;CAKjD,CAAC;IACA,CAAC;IAED;;OAEG;IACK,cAAc,CACpB,aAA4B,EAC5B,QAAyB,EACzB,QAAoC;QAEpC,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClG,MAAM,YAAY,GAAG,QAAQ,CAAC,CAAC,CAAC,SAAS,QAAQ,CAAC,IAAI,OAAO,QAAQ,CAAC,SAAS,0BAA0B,CAAC,CAAC,CAAC,EAAE,CAAC;QAE/G,OAAO,KAAK,aAAa,CAAC,IAAI;;EAEhC,aAAa,CAAC,WAAW,IAAI,wBAAwB;;;;EAIrD,YAAY,GAAG,YAAY;;;;;;;;;;;;;;;;;;;;;;EAsB3B,QAAQ,CAAC,CAAC,CAAC,sCAAsC,QAAQ,CAAC,IAAI,eAAe,CAAC,CAAC,CAAC,EAAE;;;;;;EAMlF,aAAa,CAAC,OAAO,IAAI,KAAK;CAC/B,CAAC;IACA,CAAC;IAED;;OAEG;IACK,uBAAuB,CAC7B,aAA4B,EAC5B,QAAyB,EACzB,QAAoC;QAEpC,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE;YACtD,GAAG,CAAC,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC,GAAG,eAAe,OAAO,CAAC,IAAI,iBAAiB,CAAC;YAC1E,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAA4B,CAAC,CAAC;QAEjC,MAAM,cAAc,GAAG,QAAQ,CAAC,CAAC,CAAC;YAChC,CAAC,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,MAAM,QAAQ,CAAC,IAAI,iBAAiB;SAC/D,CAAC,CAAC,CAAC,EAAE,CAAC;QAEP,MAAM,WAAW,GAAG;YAClB,IAAI,EAAE,aAAa,CAAC,IAAI;YACxB,OAAO,EAAE,aAAa,CAAC,OAAO,IAAI,OAAO;YACzC,WAAW,EAAE,aAAa,CAAC,WAAW,IAAI,EAAE;YAC5C,OAAO,EAAE,IAAI;YACb,OAAO,EAAE;gBACP,cAAc,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;gBACtE,GAAG,cAAc;gBACjB,GAAG,cAAc;gBACjB,WAAW,EAAE,mBAAmB;gBAChC,aAAa,EAAE,qBAAqB;gBACpC,cAAc,EAAE,sBAAsB;aACvC;YACD,MAAM,EAAE,aAAa,CAAC,MAAM,IAAI,EAAE;YAClC,OAAO,EAAE,aAAa,CAAC,OAAO,IAAI,KAAK;SACxC,CAAC;QAEF,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC9C,CAAC;CACF;AArUD,4CAqUC"}