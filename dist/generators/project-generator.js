"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectGenerator = void 0;
const path_1 = __importDefault(require("path"));
const template_engine_js_1 = require("./template-engine.js");
const frontend_generator_js_1 = require("./frontend-generator.js");
const service_generator_js_1 = require("./service-generator.js");
/**
 * Main project generator that orchestrates the creation of microservice projects
 */
class ProjectGenerator {
    constructor(logger, fileUtils) {
        this.logger = logger;
        this.fileUtils = fileUtils;
        this.templateEngine = new template_engine_js_1.TemplateEngine(logger, fileUtils);
        this.serviceGenerator = new service_generator_js_1.ServiceGenerator(logger, fileUtils, this.templateEngine);
        this.frontendGenerator = new frontend_generator_js_1.FrontendGenerator(logger, fileUtils, this.templateEngine);
    }
    /**
     * Generate a complete microservice project
     */
    async generateProject(projectConfig, services, frontend, options) {
        const projectPath = path_1.default.join(options.outputDir, projectConfig.name);
        // Validate project setup
        await this.validateProjectSetup(projectPath, options);
        // Create project structure
        await this.createProjectStructure(projectPath, projectConfig, options);
        // Generate services
        if (services.length > 0) {
            await this.generateServices(projectPath, services, projectConfig, options);
        }
        // Generate frontend
        if (frontend) {
            await this.generateFrontend(projectPath, frontend, projectConfig, options);
        }
        // Generate Docker and orchestration files
        await this.generateOrchestration(projectPath, projectConfig, services, frontend, options);
        // Generate root project files
        await this.generateRootFiles(projectPath, projectConfig, services, frontend, options);
    }
    /**
     * Validate project setup before generation
     */
    async validateProjectSetup(projectPath, options) {
        const isEmpty = await this.fileUtils.isDirectoryEmpty(projectPath);
        if (!isEmpty && !options.force) {
            const shouldContinue = await this.confirmOverwrite(projectPath);
            if (!shouldContinue) {
                throw new Error('Project generation cancelled');
            }
        }
        if (options.dryRun) {
            this.logger.info('Dry run mode - no files will be created');
        }
    }
    /**
     * Create basic project structure
     */
    async createProjectStructure(projectPath, _projectConfig, options) {
        this.logger.subtitle('Creating project structure...');
        if (!options.dryRun) {
            await this.fileUtils.ensureDirectory(projectPath);
            await this.fileUtils.ensureDirectory(path_1.default.join(projectPath, 'services'));
            await this.fileUtils.ensureDirectory(path_1.default.join(projectPath, 'docs'));
            await this.fileUtils.ensureDirectory(path_1.default.join(projectPath, 'scripts'));
        }
        this.logger.success('Project structure created');
    }
    /**
     * Generate all services
     */
    async generateServices(projectPath, services, projectConfig, options) {
        this.logger.subtitle('Generating services...');
        for (const service of services) {
            const servicePath = path_1.default.join(projectPath, 'services', service.name);
            if (!options.dryRun) {
                await this.fileUtils.ensureDirectory(servicePath);
            }
            await this.serviceGenerator.generateService(servicePath, service, projectConfig, options);
        }
        this.logger.success(`Generated ${services.length} service(s)`);
    }
    /**
     * Generate frontend application
     */
    async generateFrontend(projectPath, frontend, projectConfig, options) {
        this.logger.subtitle('Generating frontend...');
        const frontendPath = path_1.default.join(projectPath, frontend.name);
        if (!options.dryRun) {
            await this.fileUtils.ensureDirectory(frontendPath);
        }
        await this.frontendGenerator.generateFrontend(frontendPath, frontend, projectConfig, options);
        this.logger.success('Frontend generated');
    }
    /**
     * Generate Docker and orchestration files
     */
    async generateOrchestration(projectPath, _projectConfig, services, frontend, options) {
        this.logger.subtitle('Generating orchestration files...');
        // Generate docker-compose.yml
        const dockerComposeContent = this.generateDockerCompose(services, frontend);
        if (!options.dryRun) {
            await this.fileUtils.writeFile(path_1.default.join(projectPath, 'docker-compose.yml'), dockerComposeContent);
        }
        this.logger.success('Orchestration files generated');
    }
    /**
     * Generate root project files
     */
    async generateRootFiles(projectPath, projectConfig, services, frontend, options) {
        this.logger.subtitle('Generating root project files...');
        // Generate README.md
        const readmeContent = this.generateReadme(projectConfig, services, frontend);
        // Generate package.json for root project
        const packageJsonContent = this.generateRootPackageJson(projectConfig, services, frontend);
        if (!options.dryRun) {
            await this.fileUtils.writeFile(path_1.default.join(projectPath, 'README.md'), readmeContent);
            await this.fileUtils.writeFile(path_1.default.join(projectPath, 'package.json'), packageJsonContent);
        }
        this.logger.success('Root project files generated');
    }
    /**
     * Confirm overwrite of existing directory
     */
    async confirmOverwrite(projectPath) {
        this.logger.warn(`Directory ${projectPath} is not empty`);
        const inquirer = await Promise.resolve().then(() => __importStar(require('inquirer')));
        const { confirm } = await inquirer.default.prompt([
            {
                type: 'confirm',
                name: 'confirm',
                message: 'Do you want to continue and overwrite existing files?',
                default: false,
            },
        ]);
        return confirm;
    }
    /**
     * Generate docker-compose.yml content
     */
    generateDockerCompose(services, frontend) {
        const serviceDefinitions = services.map(service => `
  ${service.name}:
    build: ./services/${service.name}
    ports:
      - "${service.port}:${service.port}"
    environment:
      - NODE_ENV=development
      - PORT=${service.port}
    volumes:
      - ./services/${service.name}:/app
      - /app/node_modules`).join('\n');
        const frontendDefinition = frontend ? `
  ${frontend.name}:
    build: ./${frontend.name}
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=development
    volumes:
      - ./${frontend.name}:/app
      - /app/node_modules` : '';
        return `version: '3.8'

services:${serviceDefinitions}${frontendDefinition}

networks:
  default:
    driver: bridge
`;
    }
    /**
     * Generate README.md content
     */
    generateReadme(projectConfig, services, frontend) {
        const servicesList = services.map(s => `- **${s.name}** (${s.type}) - Port ${s.port}`).join('\n');
        const frontendInfo = frontend ? `\n- **${frontend.name}** (${frontend.framework}) - Frontend application` : '';
        return `# ${projectConfig.name}

${projectConfig.description || 'A microservice project'}

## Services

${servicesList}${frontendInfo}

## Getting Started

1. Install dependencies:
   \`\`\`bash
   npm install
   \`\`\`

2. Start all services:
   \`\`\`bash
   docker-compose up
   \`\`\`

3. Or start services individually:
   \`\`\`bash
   npm run dev:services
   \`\`\`

## Development

- Each service is located in the \`services/\` directory
${frontend ? `- Frontend application is in the \`${frontend.name}/\` directory` : ''}
- Use \`docker-compose\` for local development
- API documentation is available at each service's \`/docs\` endpoint

## License

${projectConfig.license || 'MIT'}
`;
    }
    /**
     * Generate root package.json content
     */
    generateRootPackageJson(projectConfig, services, frontend) {
        const serviceScripts = services.reduce((acc, service) => {
            acc[`dev:${service.name}`] = `cd services/${service.name} && npm run dev`;
            return acc;
        }, {});
        const frontendScript = frontend ? {
            [`dev:${frontend.name}`]: `cd ${frontend.name} && npm run dev`
        } : {};
        const packageJson = {
            name: projectConfig.name,
            version: projectConfig.version || '1.0.0',
            description: projectConfig.description || '',
            private: true,
            scripts: {
                'dev:services': services.map(s => `npm run dev:${s.name}`).join(' & '),
                ...serviceScripts,
                ...frontendScript,
                'docker:up': 'docker-compose up',
                'docker:down': 'docker-compose down',
                'docker:build': 'docker-compose build',
            },
            author: projectConfig.author || '',
            license: projectConfig.license || 'MIT',
        };
        return JSON.stringify(packageJson, null, 2);
    }
}
exports.ProjectGenerator = ProjectGenerator;
//# sourceMappingURL=project-generator.js.map